from flask import Blueprint, render_template, request, jsonify, session
from flask_login import login_required, current_user
from datetime import datetime, date, time
import json
from db import db
from models import DutyData, DutyPersonnel, DutyTemplate, Location, Personnel, LocationPersonnel
from datetime_utils import get_saudi_now

# إنشاء Blueprint
duties_bp = Blueprint('duties', __name__, url_prefix='/duties')

@duties_bp.route('/')
@login_required
def index():
    """صفحة كشف الواجبات الرئيسية"""
    return render_template('duties.html')

@duties_bp.route('/api/get-locations', methods=['GET'])
@login_required
def get_locations():
    """الحصول على قائمة المواقع المتاحة"""
    try:
        locations = Location.query.filter_by(status='نشط').order_by(Location.name).all()
        
        locations_data = []
        for location in locations:
            locations_data.append({
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number,
                'type': location.type,
                'description': location.description
            })
        
        return jsonify({
            'success': True,
            'locations': locations_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-location-personnel/<int:location_id>', methods=['GET'])
@login_required
def get_location_personnel(location_id):
    """الحصول على الأفراد المعينين لموقع معين"""
    try:
        print(f"🔍 البحث عن أفراد الموقع {location_id}")

        # الحصول على الأفراد المرتبطين بالموقع من خلال جدول LocationPersonnel
        # جلب جميع الأفراد المعينين للموقع بغض النظر عن حالتهم
        personnel_assignments = db.session.query(LocationPersonnel, Personnel).join(
            Personnel, LocationPersonnel.personnel_id == Personnel.id
        ).filter(
            LocationPersonnel.location_id == location_id,
            LocationPersonnel.is_active == True
        ).all()

        print(f"📊 تم العثور على {len(personnel_assignments)} تعيين نشط")

        personnel_data = []
        for assignment, person in personnel_assignments:
            # إضافة رمز للحالة
            status_icon = '✅' if person.status == 'نشط' else '⚠️'
            display_name = f"{person.rank} {person.name}"
            if person.status != 'نشط':
                display_name += f" ({person.status})"

            personnel_data.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'name': person.name,
                'rank': person.rank,
                'phone': person.phone,
                'status': person.status,
                'display_name': display_name,
                'assignment_date': assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else None
            })
            print(f"👤 {status_icon} {person.rank} {person.name} - حالة: {person.status} - معين منذ {assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else 'غير محدد'}")

        print(f"✅ إرسال {len(personnel_data)} فرد للعميل")

        return jsonify({
            'success': True,
            'personnel': personnel_data
        })

    except Exception as e:
        print(f"❌ خطأ في جلب أفراد الموقع: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-duty', methods=['POST'])
@login_required
def save_duty():
    """حفظ كشف واجب جديد"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})
        
        # التحقق من البيانات المطلوبة
        required_fields = ['location_id', 'duty_date', 'duty_time', 'duty_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})
        
        # تحويل التاريخ والوقت
        try:
            duty_date = datetime.strptime(data['duty_date'], '%Y-%m-%d').date()
            duty_time = datetime.strptime(data['duty_time'], '%H:%M').time()
        except ValueError as e:
            return jsonify({'success': False, 'error': f'تنسيق التاريخ أو الوقت غير صحيح: {str(e)}'})
        
        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=data['location_id'],
            user_id=current_user.id,
            duty_date=duty_date,
            duty_time=duty_time,
            duty_data=json.dumps(data['duty_data'], ensure_ascii=False),
            notes=data.get('notes', '')
        )
        
        db.session.add(duty_data)
        db.session.flush()  # للحصول على ID
        
        # حفظ أفراد الواجب
        if 'personnel' in data and data['personnel']:
            for person_data in data['personnel']:
                duty_personnel = DutyPersonnel(
                    duty_data_id=duty_data.id,
                    personnel_id=person_data['personnel_id'],
                    duty_position=person_data.get('position', ''),
                    duty_status=person_data.get('status', 'حاضر'),
                    notes=person_data.get('notes', '')
                )
                db.session.add(duty_personnel)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ كشف الواجب بنجاح',
            'duty_id': duty_data.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duties', methods=['GET'])
@login_required
def get_duties():
    """الحصول على قائمة كشوفات الواجبات"""
    try:
        # معاملات البحث
        location_id = request.args.get('location_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # بناء الاستعلام
        query = db.session.query(DutyData, Location).join(
            Location, DutyData.location_id == Location.id
        )
        
        # تطبيق المرشحات
        if location_id:
            query = query.filter(DutyData.location_id == location_id)
        
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date >= start_date_obj)
        
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date <= end_date_obj)
        
        # ترتيب النتائج
        query = query.order_by(DutyData.duty_date.desc(), DutyData.duty_time.desc())
        
        duties = query.all()
        
        duties_data = []
        for duty_data, location in duties:
            # الحصول على أفراد الواجب
            personnel = db.session.query(DutyPersonnel, Personnel).join(
                Personnel, DutyPersonnel.personnel_id == Personnel.id
            ).filter(DutyPersonnel.duty_data_id == duty_data.id).all()
            
            personnel_list = []
            for duty_personnel, person in personnel:
                personnel_list.append({
                    'id': person.id,
                    'name': person.name,
                    'rank': person.rank,
                    'position': duty_personnel.duty_position,
                    'status': duty_personnel.duty_status,
                    'notes': duty_personnel.notes
                })
            
            duties_data.append({
                'id': duty_data.id,
                'location': {
                    'id': location.id,
                    'name': location.name,
                    'serial_number': location.serial_number
                },
                'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
                'duty_time': duty_data.duty_time.strftime('%H:%M'),
                'duty_data': json.loads(duty_data.duty_data),
                'personnel': personnel_list,
                'notes': duty_data.notes,
                'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        return jsonify({
            'success': True,
            'duties': duties_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duty/<int:duty_id>', methods=['GET'])
@login_required
def get_duty(duty_id):
    """الحصول على تفاصيل كشف واجب محدد"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        location = Location.query.get(duty_data.location_id)
        
        # الحصول على أفراد الواجب
        personnel = db.session.query(DutyPersonnel, Personnel).join(
            Personnel, DutyPersonnel.personnel_id == Personnel.id
        ).filter(DutyPersonnel.duty_data_id == duty_id).all()
        
        personnel_list = []
        for duty_personnel, person in personnel:
            personnel_list.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'name': person.name,
                'rank': person.rank,
                'position': duty_personnel.duty_position,
                'status': duty_personnel.duty_status,
                'notes': duty_personnel.notes
            })
        
        duty_info = {
            'id': duty_data.id,
            'location': {
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number
            },
            'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
            'duty_time': duty_data.duty_time.strftime('%H:%M'),
            'duty_data': json.loads(duty_data.duty_data),
            'personnel': personnel_list,
            'notes': duty_data.notes,
            'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M'),
            'updated_at': duty_data.updated_at.strftime('%Y-%m-%d %H:%M')
        }
        
        return jsonify({
            'success': True,
            'duty': duty_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-duty/<int:duty_id>', methods=['DELETE'])
@login_required
def delete_duty(duty_id):
    """حذف كشف واجب"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        
        # حذف أفراد الواجب أولاً
        DutyPersonnel.query.filter_by(duty_data_id=duty_id).delete()
        
        # حذف كشف الواجب
        db.session.delete(duty_data)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف كشف الواجب بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Routes لقوالب الواجبات

@duties_bp.route('/api/save-template', methods=['POST'])
@login_required
def save_template():
    """حفظ قالب واجب جديد"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})

        # التحقق من البيانات المطلوبة
        required_fields = ['name', 'location_id', 'template_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})

        # إنشاء قالب جديد
        template = DutyTemplate(
            name=data['name'],
            location_id=data['location_id'],
            template_data=json.dumps(data['template_data'], ensure_ascii=False),
            created_by=current_user.id
        )

        db.session.add(template)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ القالب بنجاح',
            'template_id': template.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-templates/<int:location_id>', methods=['GET'])
@login_required
def get_templates(location_id):
    """الحصول على قوالب الواجبات لموقع معين"""
    try:
        templates = DutyTemplate.query.filter_by(
            location_id=location_id,
            is_active=True
        ).order_by(DutyTemplate.name).all()

        templates_data = []
        for template in templates:
            templates_data.append({
                'id': template.id,
                'name': template.name,
                'template_data': json.loads(template.template_data),
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'success': True,
            'templates': templates_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-template/<int:template_id>', methods=['DELETE'])
@login_required
def delete_template(template_id):
    """حذف قالب واجب"""
    try:
        template = DutyTemplate.query.get_or_404(template_id)

        # تعطيل القالب بدلاً من حذفه
        template.is_active = False
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف القالب بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-receipt', methods=['POST'])
@login_required
def save_receipt():
    """حفظ كشف الواجبات الكامل"""
    try:
        print("🔄 بدء عملية حفظ الكشف...")

        data = request.get_json()
        print(f"📋 البيانات المستلمة: {data is not None}")

        if not data:
            print("❌ لا توجد بيانات")
            return jsonify({'success': False, 'message': 'لا توجد بيانات'})

        print(f"👤 المستخدم الحالي: {current_user.id}")

        # الحصول على أول موقع متاح أو إنشاء موقع افتراضي
        first_location = Location.query.filter_by(status='نشط').first()
        if not first_location:
            # إنشاء موقع افتراضي إذا لم يوجد
            first_location = Location(
                name='موقع افتراضي',
                type='عام',
                serial_number='DEFAULT001',
                description='موقع افتراضي للكشوفات',
                status='نشط'
            )
            db.session.add(first_location)
            db.session.flush()  # للحصول على ID

        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=first_location.id,
            user_id=current_user.id,
            duty_date=datetime.now().date(),
            duty_time=datetime.now().time(),
            duty_data=json.dumps(data, ensure_ascii=False),
            notes=f"كشف واجبات - {data.get('day_name', '')}"
        )

        print("💾 إضافة البيانات إلى قاعدة البيانات...")
        db.session.add(duty_data)
        db.session.commit()

        print(f"✅ تم حفظ الكشف بنجاح - ID: {duty_data.id}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ الكشف بنجاح',
            'duty_id': duty_data.id
        })

    except Exception as e:
        print(f"❌ خطأ في حفظ الكشف: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@duties_bp.route('/api/save-draft', methods=['POST'])
@login_required
def save_draft():
    """حفظ مسودة كشف الواجب"""
    try:
        data = request.get_json()
        print(f"💾 حفظ مسودة: {len(str(data))} حرف")

        # البحث عن مسودة موجودة للمستخدم الحالي
        existing_draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id,
            location_id=None  # المسودات لا ترتبط بموقع محدد
        ).first()

        if existing_draft:
            # تحديث المسودة الموجودة
            existing_draft.template_data = json.dumps(data, ensure_ascii=False)
            existing_draft.updated_at = get_saudi_now()
            print(f"✅ تم تحديث المسودة الموجودة")
        else:
            # إنشاء مسودة جديدة
            draft = DutyTemplate(
                name=f"مسودة_{current_user.id}",
                description="مسودة تلقائية",
                template_data=json.dumps(data, ensure_ascii=False),
                created_by=current_user.id,
                location_id=None
            )
            db.session.add(draft)
            print(f"✅ تم إنشاء مسودة جديدة")

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ المسودة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/load-draft', methods=['GET'])
@login_required
def load_draft():
    """تحميل مسودة كشف الواجب"""
    try:
        # البحث عن مسودة المستخدم الحالي
        draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id,
            location_id=None
        ).first()

        if draft:
            draft_data = json.loads(draft.template_data)
            print(f"✅ تم تحميل المسودة: {len(str(draft_data))} حرف")

            return jsonify({
                'success': True,
                'data': draft_data,
                'last_updated': draft.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            print(f"⚠️ لا توجد مسودة محفوظة للمستخدم {current_user.id}")
            return jsonify({
                'success': False,
                'message': 'لا توجد مسودة محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })
