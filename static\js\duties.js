// كشف الواجبات التفاعلي - JavaScript Functions
// 🚀 بدء تحميل ملف duties.js
console.log('🚀 تحميل ملف duties.js...');

// العناوين الافتراضية لكشف الواجبات
const DEFAULT_DUTY_HEADERS = ['الرقم', 'اسم الفرد', 'الرتبة', 'منصب الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'ملاحظات'];

// العناوين الافتراضية لجدول ملاحظات الواجبات
const DEFAULT_NOTES_HEADERS = ['الرقم', 'الوقت', 'الملاحظة', 'المسؤول', 'الحالة'];

// العناوين الافتراضية لكشف الأفراد
const DEFAULT_PERSONNEL_HEADERS = ['الرقم', 'اسم الفرد', 'الرتبة', 'الرقم العسكري', 'الحالة', 'ملاحظات'];

console.log('✅ تم تحديد العناوين الافتراضية');
console.log('📋 عناوين كشف الواجبات:', DEFAULT_DUTY_HEADERS);
console.log('📋 عناوين ملاحظات الواجبات:', DEFAULT_NOTES_HEADERS);

let dutyData = {
    hijriDate: '',
    dutyDate: '',
    dutyTime: '',
    locationId: '',
    headers: [...DEFAULT_DUTY_HEADERS],
    rows: [],
    notes: ''
};

// بيانات جدول ملاحظات الواجبات
let notesData = {
    headers: [...DEFAULT_NOTES_HEADERS],
    rows: []
};

// بيانات كشف الأفراد
let personnelData = {
    headers: [...DEFAULT_PERSONNEL_HEADERS],
    rows: []
};

console.log('📊 تم تهيئة بيانات كشف الواجبات:', dutyData);
console.log('📊 تم تهيئة بيانات ملاحظات الواجبات:', notesData);
console.log('📊 تم تهيئة بيانات كشف الأفراد:', personnelData);

// قاعدة بيانات المواقع والأفراد
let locationsDatabase = [];
let locationPersonnel = [];

// متغيرات للحفظ التلقائي
let autoSaveTimeout = null;
const AUTO_SAVE_DELAY = 2000;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة صفحة كشف الواجبات...');

    // تعيين التاريخ والوقت الحالي
    setCurrentDateTime();

    // تحميل المواقع
    loadLocations();

    // تهيئة الجداول
    initializeTables();

    // تحميل البيانات المحفوظة
    loadSavedData();

    console.log('✅ تم تهيئة صفحة كشف الواجبات');
});

// تهيئة الجداول
function initializeTables() {
    console.log('📋 تهيئة الجداول...');

    // تهيئة جدول الواجبات الرئيسي
    generateTableHeader('dutyTable', 'tableHeader', dutyData.headers);
    addRow(); // إضافة صف أولي

    // تهيئة جدول ملاحظات الواجبات
    generateTableHeader('notesTable', 'notesTableHeader', notesData.headers);
    addNotesRow(); // إضافة صف أولي

    // تهيئة جدول الأفراد
    generateTableHeader('personnelTable', 'personnelTableHeader', personnelData.headers);
    addPersonnelRow(); // إضافة صف أولي

    console.log('✅ تم تهيئة جميع الجداول');
}

// تعيين التاريخ والوقت الحالي
function setCurrentDateTime() {
    const now = new Date();

    // تعيين اليوم
    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    document.getElementById('dayName').value = days[now.getDay()];

    // تعيين التاريخ الميلادي
    const today = now.toISOString().split('T')[0];
    document.getElementById('dutyDate').value = today;

    // تعيين الوقت (الساعة الحالية)
    const currentTime = now.toTimeString().slice(0, 5);
    document.getElementById('dutyTime').value = currentTime;

    // تعيين التاريخ الهجري (يمكن تحسينه لاحقاً)
    document.getElementById('hijriDate').value = getCurrentHijriDate();
}

// الحصول على التاريخ الهجري الحالي (مبسط)
function getCurrentHijriDate() {
    const now = new Date();
    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    // تقريب بسيط للتاريخ الهجري (يحتاج لمكتبة دقيقة في التطبيق الحقيقي)
    const hijriYear = 1447; // سنة تقريبية
    const hijriMonth = hijriMonths[now.getMonth() % 12];
    const hijriDay = now.getDate();

    return `${hijriDay} ${hijriMonth} ${hijriYear}هـ`;
}

// إنشاء رأس الجدول
function generateTableHeader(tableId, headerId, headers) {
    const headerRow = document.getElementById(headerId);
    headerRow.innerHTML = '';

    const tr = document.createElement('tr');
    tr.className = 'table-dark';

    headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.innerHTML = `
            <input type="text" class="form-control header-input"
                   value="${header}"
                   onchange="updateHeader('${tableId}', ${index}, this.value)"
                   style="background: transparent; border: none; color: white; text-align: center;">
        `;
        th.style.minWidth = '120px';
        tr.appendChild(th);
    });

    headerRow.appendChild(tr);
}

// تحديث رأس العمود
function updateHeader(tableId, index, newValue) {
    if (tableId === 'dutyTable') {
        dutyData.headers[index] = newValue;
    } else if (tableId === 'notesTable') {
        notesData.headers[index] = newValue;
    } else if (tableId === 'personnelTable') {
        personnelData.headers[index] = newValue;
    }

    scheduleAutoSave();
}

// إضافة عمود جديد للجدول الرئيسي
function addColumn() {
    dutyData.headers.push('عمود جديد');

    // إضافة خلية فارغة لكل صف موجود
    dutyData.rows.forEach(row => {
        row.push('');
    });

    generateTableHeader('dutyTable', 'tableHeader', dutyData.headers);
    regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);

    scheduleAutoSave();
}

// إضافة صف جديد للجدول الرئيسي
function addRow() {
    const newRow = new Array(dutyData.headers.length).fill('');
    dutyData.rows.push(newRow);

    regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
    scheduleAutoSave();
}

// تحميل قائمة المواقع
function loadLocations() {
    console.log('📍 تحميل قائمة المواقع...');

    fetch('/duties/api/get-locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                locationsDatabase = data.locations;
                populateLocationSelects();
                console.log(`✅ تم تحميل ${data.locations.length} موقع`);
            } else {
                showAlert('خطأ في تحميل المواقع: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل المواقع:', error);
            showAlert('خطأ في تحميل المواقع', 'error');
        });
}

// ملء قوائم المواقع المنسدلة
function populateLocationSelects() {
    const locationSelect = document.getElementById('locationSelect');

    // مسح الخيارات الحالية
    locationSelect.innerHTML = '<option value="">اختر الموقع</option>';

    // إضافة المواقع
    locationsDatabase.forEach(location => {
        const option = new Option(
            `${location.name} (${location.serial_number})`,
            location.id
        );
        locationSelect.appendChild(option);
    });
}

// إعادة إنشاء صفوف الجدول
function regenerateTableRows(tableId, bodyId, rows) {
    const tbody = document.getElementById(bodyId);
    tbody.innerHTML = '';

    rows.forEach((row, rowIndex) => {
        const tr = document.createElement('tr');

        row.forEach((cellValue, colIndex) => {
            const td = document.createElement('td');

            // إنشاء حقل إدخال للخلية
            if (tableId === 'dutyTable' && colIndex === 0) {
                // العمود الأول: رقم الصف
                td.innerHTML = `<span class="row-number">${rowIndex + 1}</span>`;
                td.style.textAlign = 'center';
                td.style.fontWeight = 'bold';
            } else if (tableId === 'dutyTable' && colIndex === 1) {
                // العمود الثاني: قائمة الأفراد
                td.innerHTML = createPersonnelSelect(rowIndex, cellValue);
            } else if (tableId === 'dutyTable' && colIndex === 2) {
                // العمود الثالث: الرتبة (للقراءة فقط)
                td.innerHTML = `<input type="text" class="form-control rank-input" value="${cellValue}" readonly>`;
            } else {
                // باقي الأعمدة: حقول إدخال عادية
                td.innerHTML = `
                    <input type="text" class="form-control cell-input"
                           value="${cellValue}"
                           onchange="updateCell('${tableId}', ${rowIndex}, ${colIndex}, this.value)">
                `;
            }

            tr.appendChild(td);
        });

        tbody.appendChild(tr);
    });
}

// إنشاء قائمة منسدلة للأفراد
function createPersonnelSelect(rowIndex, selectedValue) {
    let options = '<option value="">اختر الفرد</option>';

    locationPersonnel.forEach(person => {
        const selected = selectedValue == person.id ? 'selected' : '';
        options += `<option value="${person.id}" data-rank="${person.rank}" ${selected}>
                        ${person.name} (${person.rank})
                    </option>`;
    });

    return `
        <select class="form-select personnel-select"
                onchange="updatePersonnelSelection(${rowIndex}, this)">
            ${options}
        </select>
    `;
}

// تحديث اختيار الفرد وتحديث الرتبة
function updatePersonnelSelection(rowIndex, selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const rank = selectedOption.dataset.rank || '';

    // تحديث قيمة الخلية في البيانات
    dutyData.rows[rowIndex][1] = selectElement.value;
    dutyData.rows[rowIndex][2] = rank;

    // تحديث حقل الرتبة في الواجهة
    const rankInput = selectElement.closest('tr').querySelector('.rank-input');
    if (rankInput) {
        rankInput.value = rank;
    }

    scheduleAutoSave();
}

// تحديث قيمة الخلية
function updateCell(tableId, rowIndex, colIndex, newValue) {
    if (tableId === 'dutyTable') {
        dutyData.rows[rowIndex][colIndex] = newValue;
    } else if (tableId === 'notesTable') {
        notesData.rows[rowIndex][colIndex] = newValue;
    } else if (tableId === 'personnelTable') {
        personnelData.rows[rowIndex][colIndex] = newValue;
    }

    scheduleAutoSave();
}

// دوال جدول ملاحظات الواجبات
function addNotesColumn() {
    notesData.headers.push('عمود جديد');
    notesData.rows.forEach(row => row.push(''));

    generateTableHeader('notesTable', 'notesTableHeader', notesData.headers);
    regenerateTableRows('notesTable', 'notesTableBody', notesData.rows);
    scheduleAutoSave();
}

function addNotesRow() {
    const newRow = new Array(notesData.headers.length).fill('');
    notesData.rows.push(newRow);
    regenerateTableRows('notesTable', 'notesTableBody', notesData.rows);
    scheduleAutoSave();
}

function resetNotesTable() {
    notesData.headers = [...DEFAULT_NOTES_HEADERS];
    notesData.rows = [];
    generateTableHeader('notesTable', 'notesTableHeader', notesData.headers);
    addNotesRow();
    scheduleAutoSave();
}

// دوال جدول الأفراد
function addPersonnelColumn() {
    personnelData.headers.push('عمود جديد');
    personnelData.rows.forEach(row => row.push(''));

    generateTableHeader('personnelTable', 'personnelTableHeader', personnelData.headers);
    regenerateTableRows('personnelTable', 'personnelTableBody', personnelData.rows);
    scheduleAutoSave();
}

function addPersonnelRow() {
    const newRow = new Array(personnelData.headers.length).fill('');
    personnelData.rows.push(newRow);
    regenerateTableRows('personnelTable', 'personnelTableBody', personnelData.rows);
    scheduleAutoSave();
}

function resetPersonnelTable() {
    personnelData.headers = [...DEFAULT_PERSONNEL_HEADERS];
    personnelData.rows = [];
    generateTableHeader('personnelTable', 'personnelTableHeader', personnelData.headers);
    addPersonnelRow();
    scheduleAutoSave();
}

// تحميل أفراد الموقع المحدد
function loadLocationPersonnel() {
    const locationId = document.getElementById('locationSelect').value;
    dutyData.locationId = locationId;

    if (!locationId) {
        locationPersonnel = [];
        regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
        loadTemplates(null);
        return;
    }

    console.log(`👥 تحميل أفراد الموقع ${locationId}...`);

    fetch(`/duties/api/get-location-personnel/${locationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                locationPersonnel = data.personnel;
                regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
                loadTemplates(locationId);
                console.log(`✅ تم تحميل ${data.personnel.length} فرد للموقع`);
            } else {
                showAlert('خطأ في تحميل أفراد الموقع: ' + data.error, 'error');
                locationPersonnel = [];
                regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل أفراد الموقع:', error);
            showAlert('خطأ في تحميل أفراد الموقع', 'error');
            locationPersonnel = [];
            regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
        });
}

// إعادة تعيين العناوين
function resetHeaders() {
    dutyData.headers = [...DEFAULT_DUTY_HEADERS];
    dutyData.rows = [];

    generateTableHeader('dutyTable', 'tableHeader', dutyData.headers);
    addRow();
    scheduleAutoSave();
}

// جدولة الحفظ التلقائي
function scheduleAutoSave() {
    if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
    }

    autoSaveTimeout = setTimeout(() => {
        autoSave();
    }, AUTO_SAVE_DELAY);
}

// الحفظ التلقائي
function autoSave() {
    console.log('💾 حفظ تلقائي...');

    // جمع البيانات
    dutyData.hijriDate = document.getElementById('hijriDate').value;
    dutyData.dutyDate = document.getElementById('dutyDate').value;
    dutyData.dutyTime = document.getElementById('dutyTime').value;
    dutyData.locationId = document.getElementById('locationSelect').value;

    // حفظ في التخزين المحلي
    localStorage.setItem('dutyData', JSON.stringify(dutyData));
    localStorage.setItem('notesData', JSON.stringify(notesData));
    localStorage.setItem('personnelData', JSON.stringify(personnelData));
    localStorage.setItem('dutyLastSaved', new Date().toISOString());

    console.log('✅ تم الحفظ التلقائي');
}

// تحميل البيانات المحفوظة
function loadSavedData() {
    console.log('📥 تحميل البيانات المحفوظة...');

    try {
        const savedDutyData = localStorage.getItem('dutyData');
        const savedNotesData = localStorage.getItem('notesData');
        const savedPersonnelData = localStorage.getItem('personnelData');

        if (savedDutyData) {
            const parsedData = JSON.parse(savedDutyData);
            dutyData = { ...dutyData, ...parsedData };

            // تحديث الحقول
            if (dutyData.hijriDate) document.getElementById('hijriDate').value = dutyData.hijriDate;
            if (dutyData.dutyDate) document.getElementById('dutyDate').value = dutyData.dutyDate;
            if (dutyData.dutyTime) document.getElementById('dutyTime').value = dutyData.dutyTime;
            if (dutyData.locationId) document.getElementById('locationSelect').value = dutyData.locationId;

            // إعادة إنشاء الجدول
            generateTableHeader('dutyTable', 'tableHeader', dutyData.headers);
            regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
        }

        if (savedNotesData) {
            notesData = JSON.parse(savedNotesData);
            generateTableHeader('notesTable', 'notesTableHeader', notesData.headers);
            regenerateTableRows('notesTable', 'notesTableBody', notesData.rows);
        }

        if (savedPersonnelData) {
            personnelData = JSON.parse(savedPersonnelData);
            generateTableHeader('personnelTable', 'personnelTableHeader', personnelData.headers);
            regenerateTableRows('personnelTable', 'personnelTableBody', personnelData.rows);
        }

        console.log('✅ تم تحميل البيانات المحفوظة');

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات المحفوظة:', error);
    }
}

// حفظ كشف الواجب
function saveDuty() {
    console.log('💾 حفظ كشف الواجب...');

    // التحقق من البيانات المطلوبة
    const locationId = document.getElementById('locationSelect').value;
    const dutyDate = document.getElementById('dutyDate').value;
    const dutyTime = document.getElementById('dutyTime').value;

    if (!locationId || !dutyDate || !dutyTime) {
        showAlert('يرجى ملء جميع الحقول المطلوبة (الموقع، التاريخ، الوقت)', 'error');
        return;
    }

    // جمع البيانات
    const dutyInfo = {
        location_id: parseInt(locationId),
        duty_date: dutyDate,
        duty_time: dutyTime,
        duty_data: {
            headers: dutyData.headers,
            rows: dutyData.rows,
            notes_headers: notesData.headers,
            notes_rows: notesData.rows,
            personnel_headers: personnelData.headers,
            personnel_rows: personnelData.rows
        },
        personnel: extractPersonnelFromTable(),
        notes: document.getElementById('hijriDate').value
    };

    // إرسال البيانات
    fetch('/duties/api/save-duty', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dutyInfo)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ كشف الواجب بنجاح', 'success');
            console.log('✅ تم حفظ كشف الواجب:', data.duty_id);
        } else {
            showAlert('خطأ في حفظ كشف الواجب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ كشف الواجب:', error);
        showAlert('خطأ في حفظ كشف الواجب', 'error');
    });
}

// استخراج بيانات الأفراد من الجدول
function extractPersonnelFromTable() {
    const personnel = [];

    dutyData.rows.forEach((row, index) => {
        if (row[1]) { // إذا تم اختيار فرد
            personnel.push({
                personnel_id: parseInt(row[1]),
                position: row[3] || '',
                status: 'حاضر',
                notes: row[row.length - 1] || ''
            });
        }
    });

    return personnel;
}

// تحميل قوالب الموقع
function loadTemplates(locationId) {
    const templateSelect = document.getElementById('templateSelect');
    templateSelect.innerHTML = '<option value="">قالب</option>';

    if (!locationId) return;

    fetch(`/duties/api/get-templates/${locationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.templates.forEach(template => {
                    const option = new Option(template.name, template.id);
                    option.dataset.templateData = JSON.stringify(template.template_data);
                    templateSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.templates.length} قالب للموقع`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل القوالب:', error);
        });
}

// تحميل قالب محدد
function loadTemplate() {
    const templateSelect = document.getElementById('templateSelect');
    const selectedOption = templateSelect.options[templateSelect.selectedIndex];

    if (!selectedOption.dataset.templateData) return;

    try {
        const templateData = JSON.parse(selectedOption.dataset.templateData);

        if (templateData.headers) {
            dutyData.headers = [...templateData.headers];
            generateTableHeader('dutyTable', 'tableHeader', dutyData.headers);
        }

        if (templateData.rows) {
            dutyData.rows = templateData.rows.map(row => [...row]);
            regenerateTableRows('dutyTable', 'dutyTableBody', dutyData.rows);
        }

        showAlert('تم تحميل القالب بنجاح', 'success');
        scheduleAutoSave();

    } catch (error) {
        console.error('❌ خطأ في تحميل القالب:', error);
        showAlert('خطأ في تحميل القالب', 'error');
    }
}

// حفظ كقالب
function saveAsTemplate() {
    const locationId = document.getElementById('locationSelect').value;

    if (!locationId) {
        showAlert('يرجى اختيار الموقع أولاً', 'error');
        return;
    }

    const templateName = prompt('أدخل اسم القالب:');
    if (!templateName) return;

    const templateData = {
        name: templateName,
        location_id: parseInt(locationId),
        template_data: {
            headers: dutyData.headers,
            rows: dutyData.rows,
            notes_headers: notesData.headers,
            notes_rows: notesData.rows,
            personnel_headers: personnelData.headers,
            personnel_rows: personnelData.rows
        }
    };

    fetch('/duties/api/save-template', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ القالب بنجاح', 'success');
            loadTemplates(locationId);
        } else {
            showAlert('خطأ في حفظ القالب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ القالب:', error);
        showAlert('خطأ في حفظ القالب', 'error');
    });
}

// تصدير إلى Excel
function exportToExcel() {
    showAlert('ميزة التصدير قيد التطوير', 'info');
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info', duration = 5000) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

console.log('✅ تم تحميل جميع دوال كشف الواجبات بنجاح');

// إضافة صف جديد للجدول
function addDutyRow() {
    dutyRowCount++;
    
    const tbody = document.getElementById('dutyTableBody');
    const row = document.createElement('tr');
    
    row.innerHTML = `
        <td class="text-center">${dutyRowCount}</td>
        <td>
            <select class="form-select personnel-select" onchange="updateRankFromPersonnel(this)">
                <option value="">اختر الفرد</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control rank-input" readonly>
        </td>
        <td>
            <input type="text" class="form-control position-input" placeholder="منصب الواجب">
        </td>
        <td>
            <select class="form-select status-select">
                <option value="حاضر">حاضر</option>
                <option value="غائب">غائب</option>
                <option value="إجازة">إجازة</option>
                <option value="مريض">مريض</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control notes-input" placeholder="ملاحظات">
        </td>
    `;
    
    tbody.appendChild(row);
    
    // تحديث قائمة الأفراد في الصف الجديد
    updatePersonnelSelects();
}

// حذف آخر صف
function removeLastRow() {
    const tbody = document.getElementById('dutyTableBody');
    if (tbody.children.length > 1) {
        tbody.removeChild(tbody.lastElementChild);
        dutyRowCount--;
        updateRowNumbers();
    } else {
        showAlert('يجب أن يحتوي الجدول على صف واحد على الأقل', 'warning');
    }
}

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#dutyTableBody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
    dutyRowCount = rows.length;
}

// تحديث الرتبة عند اختيار الفرد
function updateRankFromPersonnel(select) {
    const row = select.closest('tr');
    const rankInput = row.querySelector('.rank-input');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.dataset.rank) {
        rankInput.value = selectedOption.dataset.rank;
    } else {
        rankInput.value = '';
    }
}

// حفظ كشف الواجب
function saveDuty() {
    console.log('💾 حفظ كشف الواجب...');
    
    // التحقق من البيانات المطلوبة
    const locationId = document.getElementById('locationSelect').value;
    const dutyDate = document.getElementById('dutyDate').value;
    const dutyTime = document.getElementById('dutyTime').value;
    
    if (!locationId || !dutyDate || !dutyTime) {
        showAlert('يرجى ملء جميع الحقول المطلوبة (الموقع، التاريخ، الوقت)', 'error');
        return;
    }
    
    // جمع بيانات الجدول
    const rows = document.querySelectorAll('#dutyTableBody tr');
    const dutyData = [];
    const personnel = [];
    
    rows.forEach((row, index) => {
        const personnelSelect = row.querySelector('.personnel-select');
        const rankInput = row.querySelector('.rank-input');
        const positionInput = row.querySelector('.position-input');
        const statusSelect = row.querySelector('.status-select');
        const notesInput = row.querySelector('.notes-input');
        
        const rowData = {
            row_number: index + 1,
            personnel_id: personnelSelect.value,
            personnel_name: personnelSelect.options[personnelSelect.selectedIndex].text,
            rank: rankInput.value,
            position: positionInput.value,
            status: statusSelect.value,
            notes: notesInput.value
        };
        
        dutyData.push(rowData);
        
        // إضافة للأفراد إذا تم اختيار فرد
        if (personnelSelect.value) {
            personnel.push({
                personnel_id: parseInt(personnelSelect.value),
                position: positionInput.value,
                status: statusSelect.value,
                notes: notesInput.value
            });
        }
    });
    
    // إعداد البيانات للإرسال
    const dutyInfo = {
        location_id: parseInt(locationId),
        duty_date: dutyDate,
        duty_time: dutyTime,
        duty_data: dutyData,
        personnel: personnel,
        notes: document.getElementById('generalNotes').value
    };
    
    // إرسال البيانات
    fetch('/duties/api/save-duty', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dutyInfo)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ كشف الواجب بنجاح', 'success');
            console.log('✅ تم حفظ كشف الواجب:', data.duty_id);
            
            // مسح النموذج بعد الحفظ
            setTimeout(() => {
                clearForm();
            }, 2000);
        } else {
            showAlert('خطأ في حفظ كشف الواجب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ كشف الواجب:', error);
        showAlert('خطأ في حفظ كشف الواجب', 'error');
    });
}

// مسح النموذج
function clearForm() {
    // مسح الحقول
    document.getElementById('locationSelect').value = '';
    document.getElementById('templateSelect').value = '';
    document.getElementById('generalNotes').value = '';

    // إعادة تعيين التاريخ والوقت
    setCurrentDateTime();

    // مسح الجدول وإضافة صف واحد
    document.getElementById('dutyTableBody').innerHTML = '';
    dutyRowCount = 0;
    addDutyRow();

    // مسح قائمة الأفراد
    locationPersonnel = [];
    updatePersonnelSelects();

    console.log('🧹 تم مسح النموذج');
}

// عرض نموذج واجب جديد
function showNewDutyForm() {
    document.getElementById('newDutyForm').style.display = 'block';
    document.getElementById('dutiesHistory').style.display = 'none';
}

// عرض نموذج واجب جديد (من الزر)
function showNewDutyModal() {
    showNewDutyForm();
    clearForm();
}

// عرض سجل الواجبات
function showDutiesHistory() {
    document.getElementById('newDutyForm').style.display = 'none';
    document.getElementById('dutiesHistory').style.display = 'block';
    loadDutiesHistory();
}

// تحميل سجل الواجبات
function loadDutiesHistory() {
    console.log('📋 تحميل سجل الواجبات...');

    // إعداد معاملات البحث
    const params = new URLSearchParams();

    const locationId = document.getElementById('filterLocation').value;
    const startDate = document.getElementById('filterStartDate').value;
    const endDate = document.getElementById('filterEndDate').value;

    if (locationId) params.append('location_id', locationId);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    fetch(`/duties/api/get-duties?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDutiesHistory(data.duties);
                console.log(`✅ تم تحميل ${data.duties.length} كشف واجب`);
            } else {
                showAlert('خطأ في تحميل سجل الواجبات: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل سجل الواجبات:', error);
            showAlert('خطأ في تحميل سجل الواجبات', 'error');
        });
}

// عرض سجل الواجبات في الجدول
function displayDutiesHistory(duties) {
    const tbody = document.getElementById('dutiesHistoryBody');
    tbody.innerHTML = '';

    if (duties.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد واجبات مسجلة</td></tr>';
        return;
    }

    duties.forEach(duty => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${duty.duty_date}</td>
            <td>${duty.duty_time}</td>
            <td>${duty.location.name}</td>
            <td>${duty.personnel.length}</td>
            <td>${duty.notes || '-'}</td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewDutyDetails(${duty.id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-sm btn-danger" onclick="confirmDeleteDuty(${duty.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تصفية الواجبات
function filterDuties() {
    loadDutiesHistory();
}

// عرض تفاصيل الواجب
function viewDutyDetails(dutyId) {
    fetch(`/duties/api/get-duty/${dutyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDutyDetailsModal(data.duty);
            } else {
                showAlert('خطأ في تحميل تفاصيل الواجب: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل تفاصيل الواجب:', error);
            showAlert('خطأ في تحميل تفاصيل الواجب', 'error');
        });
}

// عرض تفاصيل الواجب في Modal
function displayDutyDetailsModal(duty) {
    currentDutyId = duty.id;

    let personnelTable = '';
    if (duty.personnel.length > 0) {
        personnelTable = `
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الرتبة</th>
                        <th>المنصب</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${duty.personnel.map(person => `
                        <tr>
                            <td>${person.name}</td>
                            <td>${person.rank}</td>
                            <td>${person.position || '-'}</td>
                            <td>${person.status}</td>
                            <td>${person.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } else {
        personnelTable = '<p class="text-muted">لا توجد أفراد مسجلين في هذا الواجب</p>';
    }

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <strong>التاريخ:</strong> ${duty.duty_date}
            </div>
            <div class="col-md-6">
                <strong>الوقت:</strong> ${duty.duty_time}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-12">
                <strong>الموقع:</strong> ${duty.location.name} (${duty.location.serial_number})
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <strong>الأفراد المكلفين:</strong>
                ${personnelTable}
            </div>
        </div>
        ${duty.notes ? `
        <div class="row mt-3">
            <div class="col-md-12">
                <strong>ملاحظات عامة:</strong>
                <p class="border p-2 bg-light">${duty.notes}</p>
            </div>
        </div>
        ` : ''}
        <div class="row mt-2">
            <div class="col-md-6">
                <small class="text-muted">تاريخ الإنشاء: ${duty.created_at}</small>
            </div>
            <div class="col-md-6">
                <small class="text-muted">آخر تحديث: ${duty.updated_at}</small>
            </div>
        </div>
    `;

    document.getElementById('dutyDetailsBody').innerHTML = detailsHtml;

    const modal = new bootstrap.Modal(document.getElementById('dutyDetailsModal'));
    modal.show();
}

// تأكيد حذف الواجب
function confirmDeleteDuty(dutyId) {
    if (confirm('هل أنت متأكد من حذف هذا الواجب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        currentDutyId = dutyId;
        deleteDuty();
    }
}

// حذف الواجب
function deleteDuty() {
    if (!currentDutyId) return;

    fetch(`/duties/api/delete-duty/${currentDutyId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف الواجب بنجاح', 'success');

            // إغلاق Modal إذا كان مفتوحاً
            const modal = bootstrap.Modal.getInstance(document.getElementById('dutyDetailsModal'));
            if (modal) modal.hide();

            // إعادة تحميل السجل
            loadDutiesHistory();

            currentDutyId = null;
        } else {
            showAlert('خطأ في حذف الواجب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حذف الواجب:', error);
        showAlert('خطأ في حذف الواجب', 'error');
    });
}

// حفظ كقالب
function saveAsTemplate() {
    const locationId = document.getElementById('locationSelect').value;

    if (!locationId) {
        showAlert('يرجى اختيار الموقع أولاً', 'error');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('saveTemplateModal'));
    modal.show();
}

// تأكيد حفظ القالب
function confirmSaveTemplate() {
    const templateName = document.getElementById('templateName').value.trim();
    const locationId = document.getElementById('locationSelect').value;

    if (!templateName) {
        showAlert('يرجى إدخال اسم القالب', 'error');
        return;
    }

    // جمع بيانات الجدول
    const rows = document.querySelectorAll('#dutyTableBody tr');
    const templateData = {
        rows: [],
        notes: document.getElementById('generalNotes').value
    };

    rows.forEach((row, index) => {
        const personnelSelect = row.querySelector('.personnel-select');
        const positionInput = row.querySelector('.position-input');
        const statusSelect = row.querySelector('.status-select');
        const notesInput = row.querySelector('.notes-input');

        templateData.rows.push({
            personnel_id: personnelSelect.value,
            position: positionInput.value,
            status: statusSelect.value,
            notes: notesInput.value
        });
    });

    // إرسال القالب
    fetch('/duties/api/save-template', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: templateName,
            location_id: parseInt(locationId),
            template_data: templateData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ القالب بنجاح', 'success');

            // إغلاق Modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('saveTemplateModal'));
            modal.hide();

            // مسح اسم القالب
            document.getElementById('templateName').value = '';

            // إعادة تحميل القوالب
            loadTemplates(locationId);
        } else {
            showAlert('خطأ في حفظ القالب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ القالب:', error);
        showAlert('خطأ في حفظ القالب', 'error');
    });
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer');

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    alertContainer.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}
