{"military_warehouse.db": {"receipt_data": {"count": 1, "columns": ["id", "user_id", "receipt_data", "created_at", "updated_at"]}, "receipt_locations": {"count": 1, "columns": ["id", "row_index", "location_id", "timestamp", "created_by", "created_at"]}, "patrol_locations": {"count": 5, "columns": ["id", "row_index", "location_id", "timestamp", "created_by", "created_at"]}, "patrol_data": {"count": 1, "columns": ["id", "user_id", "patrol_data", "created_at", "updated_at"]}, "shifts_locations": {"count": 0, "columns": ["id", "row_index", "location_id", "timestamp", "created_by", "created_at"]}, "shifts_data": {"count": 1, "columns": ["id", "user_id", "shifts_data", "created_at", "updated_at"]}}, "instance/military_warehouse.db": {"users": {"count": 1, "columns": ["id", "username", "password_hash", "full_name", "email", "is_admin", "user_role", "role_en", "is_active", "created_at", "last_login", "updated_at"]}, "warehouses": {"count": 3, "columns": ["id", "name", "description", "location", "created_at", "updated_at"]}, "user_warehouse": {"count": 3, "columns": ["user_id", "warehouse_id"]}, "weapons": {"count": 4, "columns": ["id", "weapon_number", "serial_number", "name", "type", "status", "condition", "notes", "barcode", "qr_code", "weapon_document", "created_at", "updated_at", "warehouse_id"]}, "personnel": {"count": 5, "columns": ["id", "personnel_id", "name", "rank", "status", "phone", "notes", "created_at", "updated_at", "warehouse_id"]}, "devices": {"count": 0, "columns": ["id", "name", "type", "model", "serial_number", "status", "manufacturer", "purchase_date", "warranty_end", "notes", "created_at", "updated_at", "warehouse_id", "location"]}, "audits": {"count": 1, "columns": ["id", "audit_date", "user_id", "status", "description", "notes", "next_audit_date", "completed_at", "created_at", "updated_at", "warehouse_id"]}, "activity_logs": {"count": 11, "columns": ["id", "action", "description", "timestamp", "ip_address", "user_id", "warehouse_id"]}, "weekly_reports": {"count": 19, "columns": ["id", "week_start", "week_end", "personnel_name", "personnel_id", "national_id", "rank", "old_status", "new_status", "current_status", "change_date", "change_time", "changed_by", "change_method", "weapons", "warehouse_name", "created_at", "warehouse_id", "user_id"]}, "backup_records": {"count": 0, "columns": ["id", "filename", "timestamp", "backup_type", "file_size", "notes", "user_id", "warehouse_id"]}, "backup_schedules": {"count": 0, "columns": ["id", "schedule_type", "day_of_week", "day_of_month", "hour", "minute", "is_active", "backup_type", "last_run", "next_run", "created_at", "updated_at", "user_id", "warehouse_id"]}, "inventory_items": {"count": 2, "columns": ["id", "item_code", "name", "category", "subcategory", "brand", "model", "size", "color", "material", "quantity_in_stock", "quantity_issued", "minimum_stock", "maximum_stock", "unit_cost", "total_value", "manufacture_date", "expiry_date", "purchase_date", "status", "location", "shelf_number", "supplier", "batch_number", "serial_numbers", "specifications", "notes", "created_at", "updated_at", "warehouse_id"]}, "personnel_weapon": {"count": 3, "columns": ["personnel_id", "weapon_id", "is_primary"]}, "weapon_transactions": {"count": 10, "columns": ["id", "transaction_type", "timestamp", "notes", "weapon_id", "personnel_id", "source_warehouse_id", "target_warehouse_id", "user_id"]}, "maintenance_records": {"count": 4, "columns": ["id", "maintenance_type", "description", "start_date", "end_date", "status", "cost", "notes", "user_id", "created_at", "updated_at", "weapon_id"]}, "device_maintenance_records": {"count": 0, "columns": ["id", "maintenance_type", "description", "start_date", "end_date", "status", "cost", "notes", "created_at", "updated_at", "device_id"]}, "inventory_transactions": {"count": 0, "columns": ["id", "transaction_type", "quantity", "unit_cost", "total_cost", "reference_number", "recipient_name", "recipient_id", "recipient_rank", "recipient_unit", "from_warehouse_id", "to_warehouse_id", "reason", "notes", "approved_by", "transaction_date", "created_at", "updated_at", "item_id", "user_id"]}, "audit_items": {"count": 0, "columns": ["id", "status", "condition", "notes", "maintenance_reason", "technical_notes", "maintenance_record_id", "created_at", "updated_at", "audit_id", "weapon_id"]}}}