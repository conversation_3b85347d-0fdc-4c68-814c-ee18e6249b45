// كشف الواجبات - نسخة مبسطة
console.log('🚀 تحميل ملف duties-simple.js...');

// العناوين الافتراضية
const DEFAULT_HEADERS = ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_PATROL_HEADERS = ['الرقم', 'موقع الواجب', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_SHIFTS_HEADERS = ['الرقم', 'موقع الواجب', '2 ظهراً إلى 10 ليلاً', '10 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 2 ظهراً', 'ملاحظات المناوبين'];

// بيانات الجداول
let dutyData = {
    headers: [...DEFAULT_HEADERS],
    rows: []
};

let patrolData = {
    headers: [...DEFAULT_PATROL_HEADERS],
    rows: []
};

let shiftsData = {
    headers: [...DEFAULT_SHIFTS_HEADERS],
    rows: []
};

// قاعدة بيانات المواقع والأفراد
let locationsDatabase = [];
let locationPersonnelMap = {};

// تحميل المواقع
async function loadLocations() {
    try {
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationsDatabase = data.locations;
                console.log(`✅ تم تحميل ${data.locations.length} موقع`);
            }
        }



    } catch (error) {
        console.error('❌ خطأ في تحميل المواقع:', error);
    }
}

// تحميل أفراد الموقع
async function loadPersonnelForLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        // التحقق من البيانات المحفوظة مسبقاً
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 محاولة جلب أفراد الموقع من الخادم...`);

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        console.log(`📡 استجابة الخادم - Status: ${response.status}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 بيانات الاستجابة:', data);

            if (data.success) {
                if (data.personnel && data.personnel.length > 0) {
                    console.log(`✅ تم جلب ${data.personnel.length} فرد من قاعدة البيانات`);
                    locationPersonnelMap[locationId] = data.personnel;
                    updatePersonnelSelectsInRow(rowIndex, data.personnel);
                } else {
                    console.log('⚠️ لا يوجد أفراد مرتبطين بهذا الموقع في قاعدة البيانات');
                    clearPersonnelSelectsInRow(rowIndex);
                }
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.message || data.error);
                clearPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log(`❌ خطأ في الاستجابة: ${response.status} - ${response.statusText}`);
            clearPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ عام في تحميل أفراد الموقع:', error);
        clearPersonnelSelectsInRow(rowIndex);
    }
}

// دالة لمسح قوائم الأفراد في صف معين
function clearPersonnelSelectsInRow(rowIndex) {
    console.log(`🧹 مسح قوائم الأفراد في الصف ${rowIndex}`);

    const row = document.querySelector(`#receiptTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.innerHTML = '<option value="">اختر الفرد</option>';
        select.disabled = true;
    });
}

// تحديث قوائم الأفراد في صف
function updatePersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للصف ${rowIndex}`);

    const row = document.querySelector(`#receiptTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على الأفراد المختارين في نفس الصف (باستثناء القائمة الحالية)
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);
        console.log(`🚫 الأفراد المختارين في الصف: [${selectedPersonnelInRow.join(', ')}]`);

        personnel.forEach(person => {
            // إخفاء الفرد إذا كان مختاراً في قائمة أخرى في نفس الصف
            if (!selectedPersonnelInRow.includes(person.id.toString())) {
                const option = document.createElement('option');
                option.value = person.id;
                // استخدام display_name إذا كان متوفراً، وإلا استخدام التنسيق العادي
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${select.options.length - 1} فرد متاح`);
    });
}

// دالة للحصول على الأفراد المختارين في نفس الصف
function getSelectedPersonnelInRow(row, excludeSelect = null) {
    const selectedIds = [];
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        // تجاهل القائمة المحددة (القائمة التي يتم تحديثها حالياً)
        if (select !== excludeSelect && select.value && select.value !== '') {
            selectedIds.push(select.value);
        }
    });

    return selectedIds;
}

// دالة لتحديث قوائم الأفراد في صف بعد تغيير الاختيار
function refreshPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#receiptTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    // الحصول على الموقع المختار في هذا الصف
    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;

    // الحصول على أفراد الموقع من الذاكرة المؤقتة
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم الأفراد في الصف ${rowIndex} بعد تغيير الاختيار`);
    updatePersonnelSelectsInRow(rowIndex, personnel);
}

// تفريغ قوائم الأفراد في صف
function clearPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#receiptTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// إنشاء رأس الجدول
function generateTableHeader() {
    const thead = document.getElementById('tableHeader');
    if (!thead) return;
    
    thead.innerHTML = '';
    const headerRow = document.createElement('tr');
    
    dutyData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            // عمود الرقم - بدون أيقونات
            th.textContent = header;
        } else {
            // باقي الأعمدة مع أيقونات التحكم
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateHeader(${index}, this.value)"
                       onblur="updateHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${dutyData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
}

// إنشاء محتوى الجدول
function generateTableBody() {
    const tbody = document.getElementById('receiptTableBody');
    if (!tbody) return;
    
    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (dutyData.rows.length === 0) {
        for (let i = 0; i < 10; i++) {
            dutyData.rows.push(Array(dutyData.headers.length).fill(''));
        }
    }
    
    tbody.innerHTML = '';
    
    dutyData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');
        
        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';
            
            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${dutyData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;
                
                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex <= 7) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }
            
            row.appendChild(td);
        });
        
        tbody.appendChild(row);
    });
}

// تحديث خلية
function updateCell(rowIndex, cellIndex, value) {
    if (dutyData.rows[rowIndex]) {
        dutyData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 7) {
            refreshPersonnelSelectsInRow(rowIndex);
        }

        // حفظ البيانات تلقائياً في localStorage
        saveDataToLocalStorage();
    }
}

// معالجة تغيير الموقع في الجدول الرئيسي
function handleLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updateCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForLocation(locationId, rowIndex);
    } else {
        clearPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري عند تغيير الموقع
    console.log('💾 حفظ فوري بعد تغيير الموقع');
    saveDataToLocalStorage();
}

// معالجة تغيير الموقع في جدول الدوريات
function handlePatrolLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع دورية: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updatePatrolCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForPatrolLocation(locationId, rowIndex);
    } else {
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع الدورية');
    saveDataToLocalStorage();
}

// معالجة تغيير الموقع في جدول المناوبات
function handleShiftsLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع مناوبة: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updateShiftsCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForShiftsLocation(locationId, rowIndex);
    } else {
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع المناوبة');
    saveDataToLocalStorage();
}

// دوال لمسح قوائم الأفراد
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// حفظ البيانات في localStorage وقاعدة البيانات
function saveDataToLocalStorage() {
    try {
        const dataToSave = {
            dutyData: dutyData,
            patrolData: patrolData,
            shiftsData: shiftsData,
            formData: {
                dayName: document.getElementById('dayName').value,
                hijriDate: document.getElementById('hijriDate').value,
                gregorianDate: document.getElementById('gregorianDate').value,
                receiptNumber: document.getElementById('receiptNumber').value
            },
            timestamp: new Date().toISOString()
        };

        // حفظ محلي فوري
        localStorage.setItem('dutyFormData', JSON.stringify(dataToSave));
        console.log('💾 تم حفظ البيانات محلياً');

        // حفظ في قاعدة البيانات (مع تأخير لتجنب الطلبات المتكررة)
        saveDraftToDatabase(dataToSave);

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات محلياً:', error);
    }
}

// متغير لتأخير الحفظ
let saveTimeout = null;

// حفظ المسودة في قاعدة البيانات
function saveDraftToDatabase(data) {
    // إلغاء الطلب السابق إذا كان موجوداً
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // تأخير الحفظ لمدة ثانية واحدة فقط لتجنب الطلبات المتكررة
    saveTimeout = setTimeout(async () => {
        try {
            console.log('🌐 حفظ المسودة في قاعدة البيانات...');

            const response = await fetch('/duties/api/save-draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('✅ تم حفظ المسودة في قاعدة البيانات');
                    // إشعار صغير وسريع
                    showNotification('💾 تم الحفظ', 'success', 2000);
                } else {
                    console.error('❌ فشل في حفظ المسودة:', result.error);
                    showNotification('❌ فشل في الحفظ: ' + result.error, 'error');
                }
            } else {
                console.error('❌ خطأ في الاستجابة:', response.status);
                showNotification('❌ خطأ في الحفظ', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ المسودة:', error);
            showNotification('❌ خطأ في الاتصال', 'error');
        }
    }, 1000); // تأخير لمدة ثانية واحدة
}

// استرجاع البيانات من localStorage وقاعدة البيانات
async function loadDataFromLocalStorage() {
    try {
        // أولاً: محاولة تحميل من قاعدة البيانات
        const databaseData = await loadDraftFromDatabase();
        if (databaseData) {
            console.log('✅ تم استرجاع البيانات من قاعدة البيانات');
            return true;
        }

        // ثانياً: التحميل من localStorage كبديل
        const savedData = localStorage.getItem('dutyFormData');
        if (savedData) {
            const data = JSON.parse(savedData);
            applyLoadedData(data);
            console.log('✅ تم استرجاع البيانات المحفوظة محلياً');
            return true;
        }
    } catch (error) {
        console.error('❌ خطأ في استرجاع البيانات:', error);
    }
    return false;
}

// تحميل المسودة من قاعدة البيانات
async function loadDraftFromDatabase() {
    try {
        console.log('🌐 تحميل المسودة من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-draft');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                applyLoadedData(result.data);
                showNotification(`تم تحميل المسودة (آخر تحديث: ${result.last_updated})`, 'info');
                return true;
            } else {
                console.log('⚠️ لا توجد مسودة محفوظة في قاعدة البيانات');
                return false;
            }
        } else {
            console.error('❌ خطأ في تحميل المسودة:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المسودة من قاعدة البيانات:', error);
        return false;
    }
}

// تطبيق البيانات المحملة
function applyLoadedData(data) {
    console.log('🔄 تطبيق البيانات المحملة:', data);

    // استرجاع بيانات الجداول
    if (data.dutyData) {
        dutyData = data.dutyData;
        console.log('✅ تم استرجاع بيانات الواجبات:', dutyData.rows.length, 'صف');
    }
    if (data.patrolData) {
        patrolData = data.patrolData;
        console.log('✅ تم استرجاع بيانات الدوريات:', patrolData.rows.length, 'صف');
    }
    if (data.shiftsData) {
        shiftsData = data.shiftsData;
        console.log('✅ تم استرجاع بيانات المناوبات:', shiftsData.rows.length, 'صف');
    }

    // استرجاع بيانات النموذج
    if (data.formData) {
        if (data.formData.dayName) document.getElementById('dayName').value = data.formData.dayName;
        if (data.formData.hijriDate) document.getElementById('hijriDate').value = data.formData.hijriDate;
        if (data.formData.gregorianDate) document.getElementById('gregorianDate').value = data.formData.gregorianDate;
        if (data.formData.receiptNumber) document.getElementById('receiptNumber').value = data.formData.receiptNumber;
        console.log('✅ تم استرجاع بيانات النموذج');
    }

    // إعادة إنشاء الجداول لتطبيق البيانات المحملة
    setTimeout(() => {
        console.log('🔄 إعادة إنشاء الجداول مع البيانات المحملة...');
        generateTable();
        generatePatrolTable();
        generateShiftsTable();

        // تحميل أفراد المواقع المختارة
        restoreLocationPersonnel();
    }, 100);
}

// دالة لاستعادة أفراد المواقع المختارة
async function restoreLocationPersonnel() {
    console.log('🔄 استعادة أفراد المواقع المختارة...');

    // استعادة أفراد الواجبات الرئيسية
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    }

    // استعادة أفراد الدوريات
    for (let rowIndex = 0; rowIndex < patrolData.rows.length; rowIndex++) {
        const row = patrolData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForPatrolLocation(locationId, rowIndex);
        }
    }

    // استعادة أفراد المناوبات
    for (let rowIndex = 0; rowIndex < shiftsData.rows.length; rowIndex++) {
        const row = shiftsData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForShiftsLocation(locationId, rowIndex);
        }
    }

    console.log('✅ تم استعادة جميع أفراد المواقع');
}

// دوال مساعدة لتحميل أفراد المواقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updatePatrolPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الدوريات:', error);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updateShiftsPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد المناوبات:', error);
    }
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info', duration = 5000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 250px;
        max-width: 400px;
        font-size: 14px;
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// إنشاء الجدول الرئيسي
function generateTable() {
    generateTableHeader();
    generateTableBody();
}

// إنشاء جدول الدوريات
function generatePatrolTable() {
    generatePatrolTableHeader();
    generatePatrolTableBody();
}

// إنشاء جدول المناوبين
function generateShiftsTable() {
    generateShiftsTableHeader();
    generateShiftsTableBody();
}

// إنشاء رأس جدول الدوريات
function generatePatrolTableHeader() {
    const thead = document.getElementById('patrolTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    patrolData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updatePatrolHeader(${index}, this.value)"
                       onblur="updatePatrolHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addPatrolColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${patrolData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deletePatrolColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول الدوريات
function generatePatrolTableBody() {
    const tbody = document.getElementById('patrolTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (patrolData.rows.length === 0) {
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    patrolData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addPatrolRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${patrolData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deletePatrolRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handlePatrolLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex <= 5) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// إنشاء رأس جدول المناوبين
function generateShiftsTableHeader() {
    const thead = document.getElementById('shiftsTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    shiftsData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateShiftsHeader(${index}, this.value)"
                       onblur="updateShiftsHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addShiftsColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${shiftsData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteShiftsColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول المناوبين
function generateShiftsTableBody() {
    const tbody = document.getElementById('shiftsTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (shiftsData.rows.length === 0) {
        for (let i = 0; i < 2; i++) {
            shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    shiftsData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addShiftsRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${shiftsData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteShiftsRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleShiftsLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex <= 4) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updateShiftsCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateShiftsCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// تحديث خلايا الجداول الأخرى
function updatePatrolCell(rowIndex, cellIndex, value) {
    if (patrolData.rows[rowIndex]) {
        patrolData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 5) {
            refreshPatrolPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();
    }
}

// تحميل أفراد الموقع - جدول الدوريات
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للدوريات - الصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${locationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearPatrolPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول الدوريات
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للدوريات - الصف ${rowIndex}`);

    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول الدوريات
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function updateShiftsCell(rowIndex, cellIndex, value) {
    if (shiftsData.rows[rowIndex]) {
        shiftsData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 4) {
            refreshShiftsPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();
    }
}

// دوال مساعدة لتحديث قوائم الأفراد في الجداول الأخرى
function refreshPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد الدوريات في الصف ${rowIndex}`);
    updatePatrolPersonnelSelects(row, personnel);
}

function refreshShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد المناوبات في الصف ${rowIndex}`);
    updateShiftsPersonnelSelects(row, personnel);
}

// دوال لتحديث قوائم الأفراد مع تجنب التكرار
function updatePatrolPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach((select, index) => {
        const currentValue = select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            if (!selectedPersonnelInRow.includes(person.id.toString())) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });
    });
}

function updateShiftsPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach((select, index) => {
        const currentValue = select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            if (!selectedPersonnelInRow.includes(person.id.toString())) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });
    });
}

// تحميل أفراد الموقع - جدول المناوبين
async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للمناوبين - الصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${locationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearShiftsPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول المناوبين
function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للمناوبين - الصف ${rowIndex}`);

    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول المناوبين
function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// تحميل أفراد الموقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    if (!locationId) {
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    if (!locationId) {
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد للجداول الأخرى
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// تفريغ قوائم الأفراد للجداول الأخرى
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تهيئة صفحة كشف الواجبات...');

    // تحميل المواقع أولاً
    await loadLocations();

    // استرجاع البيانات المحفوظة من قاعدة البيانات أو محلياً
    const hasLocalData = await loadDataFromLocalStorage();

    // إنشاء جميع الجداول
    generateTable();
    generatePatrolTable();
    generateShiftsTable();

    // تعيين التاريخ الحالي فقط إذا لم توجد بيانات محفوظة
    if (!hasLocalData) {
        setCurrentDate();
    }

    console.log('✅ تم تهيئة صفحة كشف الواجبات بنجاح');
});

// تعيين التاريخ الحالي
function setCurrentDate() {
    const today = new Date();
    const gregorianDate = today.toISOString().split('T')[0];

    // تعيين التاريخ الميلادي
    document.getElementById('gregorianDate').value = gregorianDate;

    // تعيين اليوم
    const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    document.getElementById('dayName').value = days[today.getDay()];

    // تعيين رقم كشف افتراضي
    const receiptNumber = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`;
    document.getElementById('receiptNumber').value = receiptNumber;
}

// دوال إضافة الصفوف والأعمدة

// إضافة صف جديد للجدول الرئيسي
function addRow() {
    dutyData.rows.push(Array(dutyData.headers.length).fill(''));
    generateTableBody();
}

// إضافة صف بعد صف محدد
function addRowAfter(rowIndex) {
    const newRow = Array(dutyData.headers.length).fill('');
    dutyData.rows.splice(rowIndex + 1, 0, newRow);
    generateTable();
}

// حذف صف
function deleteRow(rowIndex) {
    if (dutyData.rows.length > 1) {
        dutyData.rows.splice(rowIndex, 1);
        generateTable();
    }
}

// إضافة عمود بعد عمود محدد
function addColumnAfter(columnIndex) {
    dutyData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    dutyData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generateTable();
}

// حذف عمود
function deleteColumn(columnIndex) {
    if (dutyData.headers.length > 3) {
        dutyData.headers.splice(columnIndex, 1);
        dutyData.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });
        generateTable();
    }
}

// تحديث رأس العمود
function updateHeader(columnIndex, newValue) {
    if (dutyData.headers[columnIndex]) {
        dutyData.headers[columnIndex] = newValue;
    }
}

// إضافة عمود جديد للجدول الرئيسي
function addColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        // إضافة العمود للرأس
        dutyData.headers.splice(-1, 0, newColumnName.trim()); // إدراج قبل عمود الملاحظات

        // إضافة خلية فارغة لكل صف
        dutyData.rows.forEach(row => {
            row.splice(-1, 0, ''); // إدراج قبل خلية الملاحظات
        });

        generateTable();
    }
}

// إضافة صف جديد لجدول الدوريات
function addPatrolRow() {
    patrolData.rows.push(Array(patrolData.headers.length).fill(''));
    generatePatrolTableBody();
}

// إضافة صف بعد صف محدد - جدول الدوريات
function addPatrolRowAfter(rowIndex) {
    const newRow = Array(patrolData.headers.length).fill('');
    patrolData.rows.splice(rowIndex + 1, 0, newRow);
    generatePatrolTable();
}

// حذف صف - جدول الدوريات
function deletePatrolRow(rowIndex) {
    if (patrolData.rows.length > 1) {
        patrolData.rows.splice(rowIndex, 1);
        generatePatrolTable();
    }
}

// إضافة عمود بعد عمود محدد - جدول الدوريات
function addPatrolColumnAfter(columnIndex) {
    patrolData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    patrolData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generatePatrolTable();
}

// حذف عمود - جدول الدوريات
function deletePatrolColumn(columnIndex) {
    if (patrolData.headers.length > 3) {
        patrolData.headers.splice(columnIndex, 1);
        patrolData.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });
        generatePatrolTable();
    }
}

// تحديث رأس العمود - جدول الدوريات
function updatePatrolHeader(columnIndex, newValue) {
    if (patrolData.headers[columnIndex]) {
        patrolData.headers[columnIndex] = newValue;
    }
}

// إضافة عمود جديد لجدول الدوريات
function addPatrolColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        patrolData.headers.splice(-1, 0, newColumnName.trim());
        patrolData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generatePatrolTable();
    }
}

// إضافة صف جديد لجدول المناوبين
function addShiftsRow() {
    shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    generateShiftsTableBody();
}

// إضافة صف بعد صف محدد - جدول المناوبين
function addShiftsRowAfter(rowIndex) {
    const newRow = Array(shiftsData.headers.length).fill('');
    shiftsData.rows.splice(rowIndex + 1, 0, newRow);
    generateShiftsTable();
}

// حذف صف - جدول المناوبين
function deleteShiftsRow(rowIndex) {
    if (shiftsData.rows.length > 1) {
        shiftsData.rows.splice(rowIndex, 1);
        generateShiftsTable();
    }
}

// إضافة عمود بعد عمود محدد - جدول المناوبين
function addShiftsColumnAfter(columnIndex) {
    shiftsData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    shiftsData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generateShiftsTable();
}

// حذف عمود - جدول المناوبين
function deleteShiftsColumn(columnIndex) {
    if (shiftsData.headers.length > 3) {
        shiftsData.headers.splice(columnIndex, 1);
        shiftsData.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });
        generateShiftsTable();
    }
}

// تحديث رأس العمود - جدول المناوبين
function updateShiftsHeader(columnIndex, newValue) {
    if (shiftsData.headers[columnIndex]) {
        shiftsData.headers[columnIndex] = newValue;
    }
}

// دالة حفظ الكشف
async function saveReceipt() {
    try {
        console.log('🔄 بدء عملية حفظ الكشف...');

        const receiptData = {
            day_name: document.getElementById('dayName').value,
            hijri_date: document.getElementById('hijriDate').value,
            gregorian_date: document.getElementById('gregorianDate').value,
            receipt_number: document.getElementById('receiptNumber').value,
            duty_data: dutyData,
            patrol_data: patrolData,
            shifts_data: shiftsData
        };

        console.log('📋 بيانات الكشف:', receiptData);

        // إعداد headers للطلب
        let headers = {
            'Content-Type': 'application/json'
        };

        // محاولة الحصول على CSRF token (اختياري لأنه معطل)
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const tokenValue = csrfToken.getAttribute('content');
            console.log('🔐 CSRF token:', tokenValue);
            if (tokenValue && tokenValue !== 'dummy_csrf_token') {
                headers['X-CSRFToken'] = tokenValue;
            }
        }

        console.log('📡 إرسال الطلب إلى الخادم...');
        const response = await fetch('/duties/api/save-receipt', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(receiptData)
        });

        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📄 نتيجة الحفظ:', result);

        if (result.success) {
            alert('✅ تم حفظ الكشف بنجاح!');
            // حفظ محلي كنسخة احتياطية
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
        } else {
            alert('❌ خطأ في حفظ الكشف: ' + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ الكشف:', error);

        // حفظ محلي في حالة فشل الحفظ على الخادم
        try {
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
            alert('❌ فشل الحفظ على الخادم، ولكن تم حفظ نسخة محلية.\nخطأ: ' + error.message);
        } catch (localError) {
            alert('❌ خطأ في حفظ الكشف: ' + error.message);
        }
    }
}

// مسح جميع البيانات
function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لن يمكن التراجع عن هذا الإجراء.')) {
        try {
            // مسح البيانات من localStorage
            localStorage.removeItem('dutyFormData');
            localStorage.removeItem('lastDutyReceipt');

            // إعادة تعيين البيانات
            dutyData.rows = [];
            patrolData.rows = [];
            shiftsData.rows = [];

            // مسح الحقول
            document.getElementById('dayName').value = '';
            document.getElementById('hijriDate').value = '';
            document.getElementById('gregorianDate').value = '';
            document.getElementById('receiptNumber').value = '';

            // إعادة إنشاء الجداول
            generateTable();
            generatePatrolTable();
            generateShiftsTable();

            // تعيين التاريخ الحالي
            setCurrentDate();

            alert('✅ تم مسح جميع البيانات بنجاح');
            console.log('🗑️ تم مسح جميع البيانات');

        } catch (error) {
            console.error('❌ خطأ في مسح البيانات:', error);
            alert('❌ حدث خطأ أثناء مسح البيانات');
        }
    }
}

// دالة تصدير إلى Excel
function exportToExcel() {
    try {
        // إنشاء workbook جديد
        const wb = XLSX.utils.book_new();

        // تحضير بيانات الجدول الرئيسي
        const dutySheetData = [dutyData.headers, ...dutyData.rows];
        const dutySheet = XLSX.utils.aoa_to_sheet(dutySheetData);
        XLSX.utils.book_append_sheet(wb, dutySheet, 'كشف الواجبات');

        // تحضير بيانات جدول الدوريات
        const patrolSheetData = [patrolData.headers, ...patrolData.rows];
        const patrolSheet = XLSX.utils.aoa_to_sheet(patrolSheetData);
        XLSX.utils.book_append_sheet(wb, patrolSheet, 'كشف الدوريات');

        // تحضير بيانات جدول المناوبين
        const shiftsSheetData = [shiftsData.headers, ...shiftsData.rows];
        const shiftsSheet = XLSX.utils.aoa_to_sheet(shiftsSheetData);
        XLSX.utils.book_append_sheet(wb, shiftsSheet, 'كشف المناوبين');

        // تصدير الملف
        const fileName = `كشف_الواجبات_${document.getElementById('gregorianDate').value || 'بدون_تاريخ'}.xlsx`;
        XLSX.writeFile(wb, fileName);

        alert('✅ تم تصدير الكشف بنجاح!');
    } catch (error) {
        console.error('❌ خطأ في تصدير الكشف:', error);
        alert('❌ خطأ في تصدير الكشف');
    }
}

// إضافة عمود جديد لجدول المناوبين
function addShiftsColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        shiftsData.headers.splice(-1, 0, newColumnName.trim());
        shiftsData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generateShiftsTable();
    }
}

// دوال التفريغ
function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ الكشف؟')) {
        dutyData.headers = [...DEFAULT_HEADERS];
        dutyData.rows = [];
        generateTable();
    }
}

function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول الدوريات؟')) {
        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
        patrolData.rows = [];
        generatePatrolTable();
    }
}

function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟')) {
        shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];
        shiftsData.rows = [];
        generateShiftsTable();
    }
}

console.log('✅ تم تحميل ملف duties-simple.js بنجاح');
