#!/usr/bin/env python3
"""
سكريبت اختبار النظام الجديد مع PostgreSQL
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

def test_postgresql_tables():
    """اختبار الجداول الجديدة في PostgreSQL"""
    
    DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    
    try:
        engine = create_engine(DATABASE_URL)
        
        print("🔗 الاتصال بقاعدة البيانات PostgreSQL...")
        
        with engine.connect() as conn:
            print("\n🔍 فحص الجداول الجديدة:")
            
            # قائمة الجداول المطلوب فحصها
            tables_to_check = [
                'receipt_data',
                'receipt_locations', 
                'patrol_data',
                'patrol_locations',
                'shifts_data',
                'shifts_locations'
            ]
            
            all_tables_exist = True
            total_records = 0
            
            for table in tables_to_check:
                try:
                    # فحص وجود الجدول
                    result = conn.execute(text(f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' 
                            AND table_name = '{table}'
                        )
                    """))
                    
                    table_exists = result.scalar()
                    
                    if table_exists:
                        # عدد السجلات
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        total_records += count
                        print(f"   ✅ {table}: موجود ({count} سجل)")
                    else:
                        print(f"   ❌ {table}: غير موجود")
                        all_tables_exist = False
                        
                except Exception as e:
                    print(f"   ❌ {table}: خطأ في الفحص - {str(e)}")
                    all_tables_exist = False
            
            print(f"\n📊 إجمالي السجلات: {total_records}")
            
            if all_tables_exist:
                print("✅ جميع الجداول موجودة ومتاحة!")
                return True
            else:
                print("❌ بعض الجداول مفقودة!")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        return False

def test_models_import():
    """اختبار استيراد النماذج الجديدة"""
    
    print("\n🔍 اختبار استيراد النماذج:")
    
    try:
        from models import (
            ReceiptData, ReceiptLocations, 
            PatrolData, PatrolLocations,
            ShiftsData, ShiftsLocations
        )
        print("   ✅ تم استيراد جميع النماذج بنجاح")
        return True
    except ImportError as e:
        print(f"   ❌ فشل في استيراد النماذج: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في استيراد النماذج: {str(e)}")
        return False

def test_data_operations():
    """اختبار العمليات على البيانات"""
    
    print("\n🔍 اختبار العمليات على البيانات:")
    
    try:
        from models import ReceiptData, db
        from datetime import datetime
        import json
        
        # اختبار إضافة سجل جديد
        test_data = {
            'test': True,
            'timestamp': datetime.now().isoformat(),
            'message': 'اختبار النظام الجديد'
        }
        
        new_receipt = ReceiptData(
            user_id=1,
            receipt_data=json.dumps(test_data, ensure_ascii=False)
        )
        
        db.session.add(new_receipt)
        db.session.commit()
        
        print("   ✅ تم إضافة سجل اختبار بنجاح")
        
        # اختبار قراءة السجل
        test_receipt = ReceiptData.query.filter_by(id=new_receipt.id).first()
        if test_receipt:
            print("   ✅ تم قراءة السجل بنجاح")
            
            # حذف السجل التجريبي
            db.session.delete(test_receipt)
            db.session.commit()
            print("   ✅ تم حذف السجل التجريبي")
            
            return True
        else:
            print("   ❌ فشل في قراءة السجل")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في العمليات: {str(e)}")
        return False

def test_utils_functions():
    """اختبار دوال utils المحدثة"""
    
    print("\n🔍 اختبار دوال utils:")
    
    try:
        from utils import get_external_sqlite_data
        
        # اختبار دالة الحصول على البيانات الخارجية
        external_data = get_external_sqlite_data()
        
        expected_keys = [
            'locations', 'location_equipment',
            'receipt_data', 'patrol_locations', 
            'shifts_locations', 'receipt_locations'
        ]
        
        all_keys_present = True
        for key in expected_keys:
            if key in external_data:
                print(f"   ✅ {key}: متوفر ({len(external_data[key])} عنصر)")
            else:
                print(f"   ❌ {key}: مفقود")
                all_keys_present = False
        
        if all_keys_present:
            print("   ✅ جميع المفاتيح متوفرة في البيانات الخارجية")
            return True
        else:
            print("   ❌ بعض المفاتيح مفقودة")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار utils: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النظام الجديد مع PostgreSQL")
    print("="*60)
    
    tests = [
        ("فحص الجداول", test_postgresql_tables),
        ("استيراد النماذج", test_models_import),
        ("العمليات على البيانات", test_data_operations),
        ("دوال utils", test_utils_functions)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return 0
    else:
        print(f"\n⚠️ فشل {total_tests - passed_tests} اختبار. يرجى مراجعة الأخطاء.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
