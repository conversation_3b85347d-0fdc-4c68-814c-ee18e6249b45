#!/usr/bin/env python3
"""
سكريبت نقل البيانات من ملفات SQLite المؤقتة إلى PostgreSQL
"""

import os
import sys
import sqlite3
import json
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

def migrate_sqlite_to_postgresql():
    """نقل البيانات من SQLite إلى PostgreSQL"""
    
    # إعدادات قاعدة البيانات
    DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    
    try:
        # إنشاء الاتصال بـ PostgreSQL
        pg_engine = create_engine(DATABASE_URL)
        
        print("🔗 الاتصال بقاعدة البيانات PostgreSQL...")
        
        with pg_engine.connect() as pg_conn:
            # بدء المعاملة
            trans = pg_conn.begin()

            try:
                # التحقق من وجود مستخدم افتراضي وإنشاؤه إذا لم يكن موجوداً
                print("🔍 التحقق من المستخدم الافتراضي...")
                result = pg_conn.execute(text("SELECT id FROM users WHERE id = 1"))
                user_exists = result.fetchone()

                if not user_exists:
                    print("👤 إنشاء مستخدم افتراضي...")
                    pg_conn.execute(text("""
                        INSERT INTO users (id, username, password_hash, is_admin, user_role, role_en, is_active, created_at)
                        VALUES (1, 'system', 'system_hash', true, 'مدير نظام', 'admin', true, CURRENT_TIMESTAMP)
                        ON CONFLICT (id) DO NOTHING
                    """))
                    print("   ✓ تم إنشاء المستخدم الافتراضي")
                else:
                    print("   ✓ المستخدم الافتراضي موجود")
                # نقل البيانات من military_warehouse.db
                sqlite_path = 'military_warehouse.db'
                if os.path.exists(sqlite_path):
                    print(f"📁 معالجة ملف: {sqlite_path}")
                    
                    # الاتصال بـ SQLite
                    sqlite_conn = sqlite3.connect(sqlite_path)
                    sqlite_conn.row_factory = sqlite3.Row
                    cursor = sqlite_conn.cursor()
                    
                    # نقل بيانات receipt_data
                    print("📋 نقل بيانات receipt_data...")
                    cursor.execute("SELECT * FROM receipt_data")
                    receipt_data_rows = cursor.fetchall()
                    
                    for row in receipt_data_rows:
                        pg_conn.execute(text("""
                            INSERT INTO receipt_data (user_id, receipt_data, created_at, updated_at)
                            VALUES (:user_id, :receipt_data, :created_at, :updated_at)
                        """), {
                            'user_id': row['user_id'] or 1,
                            'receipt_data': row['receipt_data'],
                            'created_at': row['created_at'],
                            'updated_at': row['updated_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(receipt_data_rows)} سجل من receipt_data")
                    
                    # نقل بيانات receipt_locations
                    print("📋 نقل بيانات receipt_locations...")
                    cursor.execute("SELECT * FROM receipt_locations")
                    receipt_locations_rows = cursor.fetchall()
                    
                    for row in receipt_locations_rows:
                        pg_conn.execute(text("""
                            INSERT INTO receipt_locations (row_index, location_id, timestamp, created_by, created_at)
                            VALUES (:row_index, :location_id, :timestamp, :created_by, :created_at)
                        """), {
                            'row_index': row['row_index'],
                            'location_id': row['location_id'],
                            'timestamp': row['timestamp'],
                            'created_by': row['created_by'] or 1,
                            'created_at': row['created_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(receipt_locations_rows)} سجل من receipt_locations")
                    
                    # نقل بيانات patrol_data
                    print("📋 نقل بيانات patrol_data...")
                    cursor.execute("SELECT * FROM patrol_data")
                    patrol_data_rows = cursor.fetchall()
                    
                    for row in patrol_data_rows:
                        pg_conn.execute(text("""
                            INSERT INTO patrol_data (user_id, patrol_data, created_at, updated_at)
                            VALUES (:user_id, :patrol_data, :created_at, :updated_at)
                        """), {
                            'user_id': row['user_id'] or 1,
                            'patrol_data': row['patrol_data'],
                            'created_at': row['created_at'],
                            'updated_at': row['updated_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(patrol_data_rows)} سجل من patrol_data")
                    
                    # نقل بيانات patrol_locations
                    print("📋 نقل بيانات patrol_locations...")
                    cursor.execute("SELECT * FROM patrol_locations")
                    patrol_locations_rows = cursor.fetchall()
                    
                    for row in patrol_locations_rows:
                        pg_conn.execute(text("""
                            INSERT INTO patrol_locations (row_index, location_id, timestamp, created_by, created_at)
                            VALUES (:row_index, :location_id, :timestamp, :created_by, :created_at)
                        """), {
                            'row_index': row['row_index'],
                            'location_id': row['location_id'],
                            'timestamp': row['timestamp'],
                            'created_by': row['created_by'] or 1,
                            'created_at': row['created_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(patrol_locations_rows)} سجل من patrol_locations")
                    
                    # نقل بيانات shifts_data
                    print("📋 نقل بيانات shifts_data...")
                    cursor.execute("SELECT * FROM shifts_data")
                    shifts_data_rows = cursor.fetchall()
                    
                    for row in shifts_data_rows:
                        pg_conn.execute(text("""
                            INSERT INTO shifts_data (user_id, shifts_data, created_at, updated_at)
                            VALUES (:user_id, :shifts_data, :created_at, :updated_at)
                        """), {
                            'user_id': row['user_id'] or 1,
                            'shifts_data': row['shifts_data'],
                            'created_at': row['created_at'],
                            'updated_at': row['updated_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(shifts_data_rows)} سجل من shifts_data")
                    
                    # نقل بيانات shifts_locations
                    print("📋 نقل بيانات shifts_locations...")
                    cursor.execute("SELECT * FROM shifts_locations")
                    shifts_locations_rows = cursor.fetchall()
                    
                    for row in shifts_locations_rows:
                        pg_conn.execute(text("""
                            INSERT INTO shifts_locations (row_index, location_id, timestamp, created_by, created_at)
                            VALUES (:row_index, :location_id, :timestamp, :created_by, :created_at)
                        """), {
                            'row_index': row['row_index'],
                            'location_id': row['location_id'],
                            'timestamp': row['timestamp'],
                            'created_by': row['created_by'] or 1,
                            'created_at': row['created_at']
                        })
                    
                    print(f"   ✓ تم نقل {len(shifts_locations_rows)} سجل من shifts_locations")
                    
                    # إغلاق اتصال SQLite
                    sqlite_conn.close()
                    
                else:
                    print(f"⚠️  الملف غير موجود: {sqlite_path}")
                
                # تأكيد المعاملة
                trans.commit()
                print("✅ تم نقل جميع البيانات بنجاح!")
                
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ خطأ في نقل البيانات: {str(e)}")
                return False
                
    except SQLAlchemyError as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def verify_migration():
    """التحقق من نجاح النقل"""
    
    DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            print("\n🔍 التحقق من البيانات المنقولة:")
            
            tables = ['receipt_data', 'receipt_locations', 'patrol_data', 
                     'patrol_locations', 'shifts_data', 'shifts_locations']
            
            total_records = 0
            for table in tables:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                total_records += count
                print(f"   📊 {table}: {count} سجل")
            
            print(f"\n📈 إجمالي السجلات المنقولة: {total_records}")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء نقل البيانات من SQLite إلى PostgreSQL")
    print("="*60)
    
    # نقل البيانات
    success = migrate_sqlite_to_postgresql()
    
    if success:
        # التحقق من النقل
        verify_migration()
        print("\n🎉 تم نقل جميع البيانات بنجاح!")
        return 0
    else:
        print("\n❌ فشل في نقل البيانات!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
