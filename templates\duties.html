{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/duties.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 style="color: var(--text-primary) !important;">
                <i class="fas fa-tasks"></i> كشف الواجبات
            </h3>
            <p class="text-muted">إنشاء وإدارة كشوف الواجبات اليومية</p>
        </div>
        <div class="col-md-4 text-right">
            <button type="button" class="btn btn-success me-2" onclick="saveDuty()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير
            </button>
        </div>
    </div>

    <!-- Duty Form -->
    <div class="card">
        <div class="card-header text-center">
            <h4 class="mb-3" style="color: var(--text-primary) !important;">كشف الواجبات</h4>
            <div class="row">
                <div class="col-md-2">
                    <label>اليوم:</label>
                    <input type="text" id="dayName" class="form-control" readonly>
                </div>
                <div class="col-md-2">
                    <label>التاريخ الهجري:</label>
                    <input type="text" id="hijriDate" class="form-control">
                </div>
                <div class="col-md-2">
                    <label>التاريخ الميلادي:</label>
                    <input type="date" id="dutyDate" class="form-control" required>
                </div>
                <div class="col-md-2">
                    <label>الوقت:</label>
                    <input type="time" id="dutyTime" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label>الموقع:</label>
                    <select class="form-select" id="locationSelect" required onchange="loadLocationPersonnel()">
                        <option value="">اختر الموقع</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label>القوالب:</label>
                    <select class="form-select" id="templateSelect" onchange="loadTemplate()">
                        <option value="">قالب</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetHeaders()">
                            <i class="fas fa-eraser"></i> تفريغ الكشف
                        </button>
                        <button type="button" class="btn btn-info btn-sm me-2" onclick="saveAsTemplate()">
                            <i class="fas fa-bookmark"></i> حفظ كقالب
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Duty Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="dutyTable">
                    <thead id="tableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="dutyTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Duty Notes Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">ملاحظات الواجبات</h5>
        </div>
        <div class="card-body">
            <!-- Duty Notes Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addNotesColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addNotesRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetNotesTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Duty Notes Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="notesTable">
                    <thead id="notesTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="notesTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Personnel Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف الأفراد</h5>
        </div>
        <div class="card-body">
            <!-- Personnel Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPersonnelColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPersonnelRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetPersonnelTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Personnel Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="personnelTable">
                    <thead id="personnelTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="personnelTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Footer Section -->
    <div class="row mt-4">
        <div class="col-md-12 text-center">
            <div class="signature-section">
                <div class="row">
                    <div class="col-md-4">
                        <div class="signature-box">
                            <p>المسؤول عن الواجب</p>
                            <div class="signature-line"></div>
                            <p class="signature-name" id="dutyOfficer">اسم المسؤول</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="signature-box">
                            <p>المراجع</p>
                            <div class="signature-line"></div>
                            <p class="signature-name" id="reviewer">اسم المراجع</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="signature-box">
                            <p>المعتمد</p>
                            <div class="signature-line"></div>
                            <p class="signature-name" id="approvedBy">اسم المعتمد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لحفظ القالب -->
<div class="modal fade" id="saveTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حفظ قالب واجب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="templateName" class="form-label">اسم القالب</label>
                    <input type="text" class="form-control" id="templateName" placeholder="أدخل اسم القالب">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmSaveTemplate()">حفظ القالب</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الواجب -->
<div class="modal fade" id="dutyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الواجب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="dutyDetailsBody">
                <!-- سيتم إضافة التفاصيل ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-danger" id="deleteDutyBtn" onclick="deleteDuty()">حذف الواجب</button>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    <!-- سيتم إضافة التنبيهات ديناميكياً -->
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/duties.js') }}"></script>
{% endblock %}
