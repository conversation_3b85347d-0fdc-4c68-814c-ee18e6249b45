/* تنسيق صفحة كشف الواجبات - مطابق لكشف الاستلامات */

/* تنسيق الجداول الرئيسية */
.receipts-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 0 !important;
    background: white !important;
    border: 2px solid var(--border-color) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.receipts-table th {
    background: linear-gradient(135deg, #2c3e50, #34495e) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    border: 1px solid var(--border-color) !important;
    padding: 12px 8px !important;
    position: relative !important;
    min-width: 120px !important;
    vertical-align: middle !important;
}

.receipts-table td {
    border: 1px solid var(--border-color) !important;
    padding: 8px !important;
    vertical-align: middle !important;
    background: white !important;
    position: relative !important;
    min-height: 40px !important;
}

.receipts-table tbody tr:hover {
    background-color: #f8f9fa !important;
    transition: background-color 0.3s ease !important;
}

/* تنسيق حقول الإدخال في رؤوس الجداول */
.header-input {
    background: transparent !important;
    border: none !important;
    color: white !important;
    text-align: center !important;
    font-weight: 600 !important;
    width: 100% !important;
    padding: 4px !important;
}

.header-input:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    border-radius: 4px !important;
}

/* تنسيق حقول الإدخال في الخلايا */
.cell-input {
    border: none !important;
    background: transparent !important;
    width: 100% !important;
    padding: 8px !important;
    text-align: center !important;
    font-size: 14px !important;
}

.cell-input:focus {
    outline: 2px solid #007bff !important;
    border-radius: 4px !important;
    background: #f8f9fa !important;
}

/* تنسيق قوائم الأفراد */
.personnel-select {
    border: none !important;
    background: transparent !important;
    width: 100% !important;
    padding: 8px !important;
    text-align: center !important;
    font-size: 14px !important;
}

.personnel-select:focus {
    outline: 2px solid #007bff !important;
    border-radius: 4px !important;
    background: #f8f9fa !important;
}

/* حقل الرتبة (للقراءة فقط) */
.rank-input {
    background-color: #f8f9fa !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    border: none !important;
    width: 100% !important;
    padding: 8px !important;
    text-align: center !important;
    cursor: not-allowed !important;
}

/* تنسيق أرقام الصفوف */
.row-number {
    color: #495057 !important;
    font-weight: bold !important;
    font-size: 16px !important;
    display: block !important;
    text-align: center !important;
}

/* تنسيق أزرار التحكم */
.table-controls {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    margin: 0 2px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border: none;
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* تنسيق حقول التاريخ والوقت في الرأس */
.card-header input,
.card-header select {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    text-align: center;
    transition: all 0.3s ease;
}

.card-header input:focus,
.card-header select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.card-header label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

/* تنسيق منطقة التوقيعات */
.signature-section {
    margin-top: 40px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.signature-box {
    text-align: center;
    padding: 20px;
}

.signature-box p {
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
}

.signature-line {
    width: 200px;
    height: 2px;
    background: #495057;
    margin: 20px auto;
    border-radius: 1px;
}

.signature-name {
    font-weight: 500;
    color: #6c757d;
    font-style: italic;
    margin-top: 10px;
}

/* تنسيق رؤوس الكروت */
.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    padding: 20px;
}

.card-header h4,
.card-header h5 {
    color: #495057 !important;
    font-weight: 600;
    margin-bottom: 15px;
}

/* تنسيق الكروت */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    overflow: hidden;
}

.card-body {
    padding: 25px;
}

/* حقول التاريخ والوقت */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

input[type="date"],
input[type="time"] {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

input[type="date"]:focus,
input[type="time"]:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* قوائم الاختيار */
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 16px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* منطقة الملاحظات العامة */
#generalNotes {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 16px;
    resize: vertical;
    min-height: 100px;
    transition: all 0.3s ease;
}

#generalNotes:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* أزرار الحفظ الرئيسية */
.btn-lg {
    padding: 12px 30px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 10px;
    margin: 0 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(25, 135, 84, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-info:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(13, 202, 240, 0.4);
}

/* سجل الواجبات */
.duties-history {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* جدول سجل الواجبات */
#dutiesHistoryTable {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#dutiesHistoryTable thead th {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    font-weight: 600;
    text-align: center;
    border: none;
    padding: 15px 10px;
}

#dutiesHistoryTable tbody td {
    padding: 12px 10px;
    vertical-align: middle;
    border-color: #e9ecef;
}

#dutiesHistoryTable tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.3s ease;
}

/* أزرار الإجراءات في الجدول */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    margin: 0 2px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* مرشحات البحث */
.duties-history .row.mb-3 {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Modal تفاصيل الواجب */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px 25px;
}

/* جدول الأفراد في Modal */
.modal-body .table {
    margin-top: 15px;
    border-radius: 8px;
    overflow: hidden;
}

.modal-body .table thead th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* رسائل التنبيه */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .duty-form {
        padding: 15px;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 16px;
        margin: 5px;
        display: block;
        width: 100%;
    }
    
    .duty-table {
        font-size: 14px;
    }
    
    .duty-table .form-select,
    .duty-table .form-control {
        font-size: 13px;
        padding: 6px 8px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .btn, .modal, .alert {
        display: none !important;
    }
    
    .duty-form {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .duty-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .duty-table thead th {
        background: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #000;
    }
    
    .duty-table tbody td {
        border: 1px solid #000;
    }
}
