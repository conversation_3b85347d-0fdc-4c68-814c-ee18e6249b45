#!/usr/bin/env python3
"""
سكريبت لتحليل قواعد البيانات المؤقتة الموجودة
"""

import sqlite3
import os
import json

def analyze_database(db_path, db_name):
    """تحليل قاعدة بيانات SQLite"""
    print(f"\n{'='*50}")
    print(f"تحليل قاعدة البيانات: {db_name}")
    print(f"المسار: {db_path}")
    print(f"{'='*50}")
    
    if not os.path.exists(db_path):
        print(f"❌ الملف غير موجود: {db_path}")
        return {}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحصول على قائمة الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ لا توجد جداول في قاعدة البيانات")
            conn.close()
            return {}
        
        db_info = {}
        total_records = 0
        
        for table in tables:
            table_name = table[0]
            if table_name == 'sqlite_sequence':
                continue
                
            print(f"\n📋 الجدول: {table_name}")
            
            # عدد السجلات
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            total_records += count
            print(f"   📊 عدد السجلات: {count}")
            
            # هيكل الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"   🏗️  الأعمدة:")
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
            
            # عينة من البيانات (أول 3 سجلات)
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = cursor.fetchall()
                print(f"   📝 عينة من البيانات:")
                for i, row in enumerate(sample_data, 1):
                    print(f"      {i}. {row}")
            
            db_info[table_name] = {
                'count': count,
                'columns': [col[1] for col in columns]
            }
        
        print(f"\n📈 إجمالي السجلات في قاعدة البيانات: {total_records}")
        conn.close()
        return db_info
        
    except Exception as e:
        print(f"❌ خطأ في تحليل قاعدة البيانات: {str(e)}")
        return {}

def main():
    """الدالة الرئيسية"""
    print("🔍 تحليل قواعد البيانات المؤقتة")
    print("="*60)
    
    databases_to_check = [
        ('military_warehouse.db', 'قاعدة البيانات الرئيسية المؤقتة'),
        ('instance/military_warehouse.db', 'قاعدة البيانات في مجلد instance'),
        ('locations.db', 'قاعدة بيانات المواقع')
    ]
    
    all_db_info = {}
    
    for db_path, db_name in databases_to_check:
        db_info = analyze_database(db_path, db_name)
        if db_info:
            all_db_info[db_path] = db_info
    
    # ملخص النتائج
    print(f"\n{'='*60}")
    print("📋 ملخص النتائج:")
    print(f"{'='*60}")
    
    if not all_db_info:
        print("❌ لم يتم العثور على أي قواعد بيانات مؤقتة")
        return
    
    total_tables = 0
    total_records = 0
    
    for db_path, db_info in all_db_info.items():
        print(f"\n📁 {db_path}:")
        for table_name, table_info in db_info.items():
            print(f"   - {table_name}: {table_info['count']} سجل")
            total_tables += 1
            total_records += table_info['count']
    
    print(f"\n📊 الإحصائيات الإجمالية:")
    print(f"   - عدد قواعد البيانات: {len(all_db_info)}")
    print(f"   - عدد الجداول: {total_tables}")
    print(f"   - إجمالي السجلات: {total_records}")
    
    # حفظ النتائج في ملف JSON
    with open('temp_databases_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(all_db_info, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 تم حفظ التحليل في: temp_databases_analysis.json")

if __name__ == "__main__":
    main()
