{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/duties.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-clipboard-list"></i>
                        كشف الواجبات
                    </h3>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="showNewDutyModal()">
                            <i class="fas fa-plus"></i> واجب جديد
                        </button>
                        <button type="button" class="btn btn-info" onclick="showDutiesHistory()">
                            <i class="fas fa-history"></i> سجل الواجبات
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- نموذج إنشاء واجب جديد -->
                    <div id="newDutyForm" class="duty-form">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="dutyDate" class="form-label">تاريخ الواجب</label>
                                <input type="date" class="form-control" id="dutyDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="dutyTime" class="form-label">وقت الواجب</label>
                                <input type="time" class="form-control" id="dutyTime" required>
                            </div>
                            <div class="col-md-4">
                                <label for="locationSelect" class="form-label">الموقع</label>
                                <select class="form-select" id="locationSelect" required onchange="loadLocationPersonnel()">
                                    <option value="">اختر الموقع</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="templateSelect" class="form-label">القوالب</label>
                                <select class="form-select" id="templateSelect" onchange="loadTemplate()">
                                    <option value="">اختر قالب</option>
                                </select>
                            </div>
                        </div>

                        <!-- جدول الواجبات -->
                        <div class="table-responsive">
                            <table class="table table-bordered duty-table" id="dutyTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 5%">#</th>
                                        <th style="width: 25%">اسم الفرد</th>
                                        <th style="width: 15%">الرتبة</th>
                                        <th style="width: 20%">منصب الواجب</th>
                                        <th style="width: 15%">الحالة</th>
                                        <th style="width: 20%">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="dutyTableBody">
                                    <!-- سيتم إضافة الصفوف ديناميكياً -->
                                </tbody>
                            </table>
                        </div>

                        <!-- أزرار إضافة الصفوف -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="addDutyRow()">
                                    <i class="fas fa-plus"></i> إضافة صف
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeLastRow()">
                                    <i class="fas fa-minus"></i> حذف آخر صف
                                </button>
                            </div>
                        </div>

                        <!-- ملاحظات عامة -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label for="generalNotes" class="form-label">ملاحظات عامة</label>
                                <textarea class="form-control" id="generalNotes" rows="3" placeholder="أدخل أي ملاحظات عامة حول الواجب..."></textarea>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <button type="button" class="btn btn-success btn-lg" onclick="saveDuty()">
                                    <i class="fas fa-save"></i> حفظ كشف الواجب
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i> مسح النموذج
                                </button>
                                <button type="button" class="btn btn-info btn-lg" onclick="saveAsTemplate()">
                                    <i class="fas fa-bookmark"></i> حفظ كقالب
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- جدول سجل الواجبات -->
                    <div id="dutiesHistory" class="duties-history" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="filterLocation" class="form-label">تصفية بالموقع</label>
                                <select class="form-select" id="filterLocation" onchange="filterDuties()">
                                    <option value="">جميع المواقع</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterStartDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="filterStartDate" onchange="filterDuties()">
                            </div>
                            <div class="col-md-3">
                                <label for="filterEndDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="filterEndDate" onchange="filterDuties()">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="btn btn-primary" onclick="loadDutiesHistory()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="showNewDutyForm()">
                                    <i class="fas fa-arrow-left"></i> عودة
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="dutiesHistoryTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الوقت</th>
                                        <th>الموقع</th>
                                        <th>عدد الأفراد</th>
                                        <th>الملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="dutiesHistoryBody">
                                    <!-- سيتم إضافة البيانات ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لحفظ القالب -->
<div class="modal fade" id="saveTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حفظ قالب واجب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="templateName" class="form-label">اسم القالب</label>
                    <input type="text" class="form-control" id="templateName" placeholder="أدخل اسم القالب">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmSaveTemplate()">حفظ القالب</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل الواجب -->
<div class="modal fade" id="dutyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الواجب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="dutyDetailsBody">
                <!-- سيتم إضافة التفاصيل ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-danger" id="deleteDutyBtn" onclick="deleteDuty()">حذف الواجب</button>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
    <!-- سيتم إضافة التنبيهات ديناميكياً -->
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/duties.js') }}"></script>
{% endblock %}
