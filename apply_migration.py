#!/usr/bin/env python3
"""
سكريبت لتطبيق الهجرة وإنشاء الجداول الجديدة في PostgreSQL
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

def create_temp_tables():
    """إنشاء الجداول المؤقتة في PostgreSQL"""
    
    # إعدادات قاعدة البيانات
    DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    
    try:
        # إنشاء الاتصال
        engine = create_engine(DATABASE_URL)
        
        print("🔗 الاتصال بقاعدة البيانات PostgreSQL...")
        
        with engine.connect() as conn:
            # بدء المعاملة
            trans = conn.begin()
            
            try:
                print("📋 إنشاء جدول receipt_data...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS receipt_data (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER NOT NULL DEFAULT 1,
                        receipt_data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول receipt_locations...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS receipt_locations (
                        id SERIAL PRIMARY KEY,
                        row_index INTEGER NOT NULL,
                        location_id VARCHAR(50) NOT NULL,
                        timestamp VARCHAR(50) NOT NULL,
                        created_by INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول patrol_data...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS patrol_data (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER NOT NULL DEFAULT 1,
                        patrol_data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول patrol_locations...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS patrol_locations (
                        id SERIAL PRIMARY KEY,
                        row_index INTEGER NOT NULL,
                        location_id VARCHAR(50) NOT NULL,
                        timestamp VARCHAR(50) NOT NULL,
                        created_by INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول shifts_data...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS shifts_data (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER NOT NULL DEFAULT 1,
                        shifts_data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول shifts_locations...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS shifts_locations (
                        id SERIAL PRIMARY KEY,
                        row_index INTEGER NOT NULL,
                        location_id VARCHAR(50) NOT NULL,
                        timestamp VARCHAR(50) NOT NULL,
                        created_by INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                print("🔍 إنشاء الفهارس...")
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_receipt_locations_row 
                    ON receipt_locations(row_index)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_patrol_locations_row 
                    ON patrol_locations(row_index)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_shifts_locations_row 
                    ON shifts_locations(row_index)
                """))
                
                # تأكيد المعاملة
                trans.commit()
                print("✅ تم إنشاء جميع الجداول والفهارس بنجاح!")
                
                # التحقق من الجداول المنشأة
                print("\n🔍 التحقق من الجداول المنشأة:")
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name IN ('receipt_data', 'receipt_locations', 'patrol_data', 
                                      'patrol_locations', 'shifts_data', 'shifts_locations')
                    ORDER BY table_name
                """))
                
                tables = result.fetchall()
                for table in tables:
                    print(f"   ✓ {table[0]}")
                
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
                return False
                
    except SQLAlchemyError as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء الجداول المؤقتة في PostgreSQL")
    print("="*60)
    
    success = create_temp_tables()
    
    if success:
        print("\n🎉 تم إنشاء جميع الجداول بنجاح!")
        print("📝 الجداول الجديدة:")
        print("   - receipt_data: بيانات الكشوفات")
        print("   - receipt_locations: مواقع الكشوفات")
        print("   - patrol_data: بيانات الدوريات")
        print("   - patrol_locations: مواقع الدوريات")
        print("   - shifts_data: بيانات المناوبات")
        print("   - shifts_locations: مواقع المناوبات")
        return 0
    else:
        print("\n❌ فشل في إنشاء الجداول!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
