// متغيرات عامة
let currentDutyId = null;
let locationPersonnel = [];
let availableLocations = [];
let dutyRowCount = 0;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة صفحة كشف الواجبات...');
    
    // تعيين التاريخ والوقت الحالي
    setCurrentDateTime();
    
    // تحميل المواقع
    loadLocations();
    
    // إضافة صف أولي
    addDutyRow();
    
    console.log('✅ تم تهيئة صفحة كشف الواجبات');
});

// تعيين التاريخ والوقت الحالي
function setCurrentDateTime() {
    const now = new Date();
    
    // تعيين التاريخ
    const today = now.toISOString().split('T')[0];
    document.getElementById('dutyDate').value = today;
    
    // تعيين الوقت (الساعة الحالية)
    const currentTime = now.toTimeString().slice(0, 5);
    document.getElementById('dutyTime').value = currentTime;
}

// تحميل قائمة المواقع
function loadLocations() {
    console.log('📍 تحميل قائمة المواقع...');
    
    fetch('/duties/api/get-locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableLocations = data.locations;
                populateLocationSelects();
                console.log(`✅ تم تحميل ${data.locations.length} موقع`);
            } else {
                showAlert('خطأ في تحميل المواقع: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل المواقع:', error);
            showAlert('خطأ في تحميل المواقع', 'error');
        });
}

// ملء قوائم المواقع المنسدلة
function populateLocationSelects() {
    const locationSelect = document.getElementById('locationSelect');
    const filterLocation = document.getElementById('filterLocation');
    
    // مسح الخيارات الحالية
    locationSelect.innerHTML = '<option value="">اختر الموقع</option>';
    if (filterLocation) {
        filterLocation.innerHTML = '<option value="">جميع المواقع</option>';
    }
    
    // إضافة المواقع
    availableLocations.forEach(location => {
        const option = new Option(
            `${location.name} (${location.serial_number})`,
            location.id
        );
        locationSelect.appendChild(option.cloneNode(true));
        
        if (filterLocation) {
            filterLocation.appendChild(option);
        }
    });
}

// تحميل أفراد الموقع المحدد
function loadLocationPersonnel() {
    const locationId = document.getElementById('locationSelect').value;
    
    if (!locationId) {
        locationPersonnel = [];
        updatePersonnelSelects();
        loadTemplates(null);
        return;
    }
    
    console.log(`👥 تحميل أفراد الموقع ${locationId}...`);
    
    fetch(`/duties/api/get-location-personnel/${locationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                locationPersonnel = data.personnel;
                updatePersonnelSelects();
                loadTemplates(locationId);
                console.log(`✅ تم تحميل ${data.personnel.length} فرد للموقع`);
            } else {
                showAlert('خطأ في تحميل أفراد الموقع: ' + data.error, 'error');
                locationPersonnel = [];
                updatePersonnelSelects();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل أفراد الموقع:', error);
            showAlert('خطأ في تحميل أفراد الموقع', 'error');
            locationPersonnel = [];
            updatePersonnelSelects();
        });
}

// تحديث قوائم الأفراد في الجدول
function updatePersonnelSelects() {
    const personnelSelects = document.querySelectorAll('.personnel-select');
    
    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الفرد</option>';
        
        locationPersonnel.forEach(person => {
            const option = new Option(
                `${person.name} (${person.rank})`,
                person.id
            );
            option.dataset.rank = person.rank;
            option.dataset.personnelId = person.personnel_id;
            select.appendChild(option);
        });
        
        // استعادة القيمة المحددة سابقاً إن وجدت
        if (currentValue) {
            select.value = currentValue;
        }
    });
}

// تحميل قوالب الموقع
function loadTemplates(locationId) {
    const templateSelect = document.getElementById('templateSelect');
    templateSelect.innerHTML = '<option value="">اختر قالب</option>';
    
    if (!locationId) return;
    
    fetch(`/duties/api/get-templates/${locationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.templates.forEach(template => {
                    const option = new Option(template.name, template.id);
                    option.dataset.templateData = JSON.stringify(template.template_data);
                    templateSelect.appendChild(option);
                });
                console.log(`✅ تم تحميل ${data.templates.length} قالب للموقع`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل القوالب:', error);
        });
}

// تحميل قالب محدد
function loadTemplate() {
    const templateSelect = document.getElementById('templateSelect');
    const selectedOption = templateSelect.options[templateSelect.selectedIndex];
    
    if (!selectedOption.dataset.templateData) return;
    
    try {
        const templateData = JSON.parse(selectedOption.dataset.templateData);
        
        // مسح الجدول الحالي
        document.getElementById('dutyTableBody').innerHTML = '';
        dutyRowCount = 0;
        
        // إضافة صفوف القالب
        templateData.rows.forEach(rowData => {
            addDutyRow();
            const lastRow = document.querySelector('#dutyTableBody tr:last-child');
            
            // ملء البيانات
            const personnelSelect = lastRow.querySelector('.personnel-select');
            const positionInput = lastRow.querySelector('.position-input');
            const statusSelect = lastRow.querySelector('.status-select');
            const notesInput = lastRow.querySelector('.notes-input');
            
            if (rowData.personnel_id) personnelSelect.value = rowData.personnel_id;
            if (rowData.position) positionInput.value = rowData.position;
            if (rowData.status) statusSelect.value = rowData.status;
            if (rowData.notes) notesInput.value = rowData.notes;
            
            // تحديث الرتبة
            updateRankFromPersonnel(personnelSelect);
        });
        
        // ملء الملاحظات العامة
        if (templateData.notes) {
            document.getElementById('generalNotes').value = templateData.notes;
        }
        
        showAlert('تم تحميل القالب بنجاح', 'success');
        
    } catch (error) {
        console.error('❌ خطأ في تحميل القالب:', error);
        showAlert('خطأ في تحميل القالب', 'error');
    }
}

// إضافة صف جديد للجدول
function addDutyRow() {
    dutyRowCount++;
    
    const tbody = document.getElementById('dutyTableBody');
    const row = document.createElement('tr');
    
    row.innerHTML = `
        <td class="text-center">${dutyRowCount}</td>
        <td>
            <select class="form-select personnel-select" onchange="updateRankFromPersonnel(this)">
                <option value="">اختر الفرد</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control rank-input" readonly>
        </td>
        <td>
            <input type="text" class="form-control position-input" placeholder="منصب الواجب">
        </td>
        <td>
            <select class="form-select status-select">
                <option value="حاضر">حاضر</option>
                <option value="غائب">غائب</option>
                <option value="إجازة">إجازة</option>
                <option value="مريض">مريض</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control notes-input" placeholder="ملاحظات">
        </td>
    `;
    
    tbody.appendChild(row);
    
    // تحديث قائمة الأفراد في الصف الجديد
    updatePersonnelSelects();
}

// حذف آخر صف
function removeLastRow() {
    const tbody = document.getElementById('dutyTableBody');
    if (tbody.children.length > 1) {
        tbody.removeChild(tbody.lastElementChild);
        dutyRowCount--;
        updateRowNumbers();
    } else {
        showAlert('يجب أن يحتوي الجدول على صف واحد على الأقل', 'warning');
    }
}

// تحديث أرقام الصفوف
function updateRowNumbers() {
    const rows = document.querySelectorAll('#dutyTableBody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
    dutyRowCount = rows.length;
}

// تحديث الرتبة عند اختيار الفرد
function updateRankFromPersonnel(select) {
    const row = select.closest('tr');
    const rankInput = row.querySelector('.rank-input');
    const selectedOption = select.options[select.selectedIndex];
    
    if (selectedOption.dataset.rank) {
        rankInput.value = selectedOption.dataset.rank;
    } else {
        rankInput.value = '';
    }
}

// حفظ كشف الواجب
function saveDuty() {
    console.log('💾 حفظ كشف الواجب...');
    
    // التحقق من البيانات المطلوبة
    const locationId = document.getElementById('locationSelect').value;
    const dutyDate = document.getElementById('dutyDate').value;
    const dutyTime = document.getElementById('dutyTime').value;
    
    if (!locationId || !dutyDate || !dutyTime) {
        showAlert('يرجى ملء جميع الحقول المطلوبة (الموقع، التاريخ، الوقت)', 'error');
        return;
    }
    
    // جمع بيانات الجدول
    const rows = document.querySelectorAll('#dutyTableBody tr');
    const dutyData = [];
    const personnel = [];
    
    rows.forEach((row, index) => {
        const personnelSelect = row.querySelector('.personnel-select');
        const rankInput = row.querySelector('.rank-input');
        const positionInput = row.querySelector('.position-input');
        const statusSelect = row.querySelector('.status-select');
        const notesInput = row.querySelector('.notes-input');
        
        const rowData = {
            row_number: index + 1,
            personnel_id: personnelSelect.value,
            personnel_name: personnelSelect.options[personnelSelect.selectedIndex].text,
            rank: rankInput.value,
            position: positionInput.value,
            status: statusSelect.value,
            notes: notesInput.value
        };
        
        dutyData.push(rowData);
        
        // إضافة للأفراد إذا تم اختيار فرد
        if (personnelSelect.value) {
            personnel.push({
                personnel_id: parseInt(personnelSelect.value),
                position: positionInput.value,
                status: statusSelect.value,
                notes: notesInput.value
            });
        }
    });
    
    // إعداد البيانات للإرسال
    const dutyInfo = {
        location_id: parseInt(locationId),
        duty_date: dutyDate,
        duty_time: dutyTime,
        duty_data: dutyData,
        personnel: personnel,
        notes: document.getElementById('generalNotes').value
    };
    
    // إرسال البيانات
    fetch('/duties/api/save-duty', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dutyInfo)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ كشف الواجب بنجاح', 'success');
            console.log('✅ تم حفظ كشف الواجب:', data.duty_id);
            
            // مسح النموذج بعد الحفظ
            setTimeout(() => {
                clearForm();
            }, 2000);
        } else {
            showAlert('خطأ في حفظ كشف الواجب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ كشف الواجب:', error);
        showAlert('خطأ في حفظ كشف الواجب', 'error');
    });
}

// مسح النموذج
function clearForm() {
    // مسح الحقول
    document.getElementById('locationSelect').value = '';
    document.getElementById('templateSelect').value = '';
    document.getElementById('generalNotes').value = '';

    // إعادة تعيين التاريخ والوقت
    setCurrentDateTime();

    // مسح الجدول وإضافة صف واحد
    document.getElementById('dutyTableBody').innerHTML = '';
    dutyRowCount = 0;
    addDutyRow();

    // مسح قائمة الأفراد
    locationPersonnel = [];
    updatePersonnelSelects();

    console.log('🧹 تم مسح النموذج');
}

// عرض نموذج واجب جديد
function showNewDutyForm() {
    document.getElementById('newDutyForm').style.display = 'block';
    document.getElementById('dutiesHistory').style.display = 'none';
}

// عرض نموذج واجب جديد (من الزر)
function showNewDutyModal() {
    showNewDutyForm();
    clearForm();
}

// عرض سجل الواجبات
function showDutiesHistory() {
    document.getElementById('newDutyForm').style.display = 'none';
    document.getElementById('dutiesHistory').style.display = 'block';
    loadDutiesHistory();
}

// تحميل سجل الواجبات
function loadDutiesHistory() {
    console.log('📋 تحميل سجل الواجبات...');

    // إعداد معاملات البحث
    const params = new URLSearchParams();

    const locationId = document.getElementById('filterLocation').value;
    const startDate = document.getElementById('filterStartDate').value;
    const endDate = document.getElementById('filterEndDate').value;

    if (locationId) params.append('location_id', locationId);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    fetch(`/duties/api/get-duties?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDutiesHistory(data.duties);
                console.log(`✅ تم تحميل ${data.duties.length} كشف واجب`);
            } else {
                showAlert('خطأ في تحميل سجل الواجبات: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل سجل الواجبات:', error);
            showAlert('خطأ في تحميل سجل الواجبات', 'error');
        });
}

// عرض سجل الواجبات في الجدول
function displayDutiesHistory(duties) {
    const tbody = document.getElementById('dutiesHistoryBody');
    tbody.innerHTML = '';

    if (duties.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد واجبات مسجلة</td></tr>';
        return;
    }

    duties.forEach(duty => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${duty.duty_date}</td>
            <td>${duty.duty_time}</td>
            <td>${duty.location.name}</td>
            <td>${duty.personnel.length}</td>
            <td>${duty.notes || '-'}</td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewDutyDetails(${duty.id})">
                    <i class="fas fa-eye"></i> عرض
                </button>
                <button class="btn btn-sm btn-danger" onclick="confirmDeleteDuty(${duty.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تصفية الواجبات
function filterDuties() {
    loadDutiesHistory();
}

// عرض تفاصيل الواجب
function viewDutyDetails(dutyId) {
    fetch(`/duties/api/get-duty/${dutyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDutyDetailsModal(data.duty);
            } else {
                showAlert('خطأ في تحميل تفاصيل الواجب: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل تفاصيل الواجب:', error);
            showAlert('خطأ في تحميل تفاصيل الواجب', 'error');
        });
}

// عرض تفاصيل الواجب في Modal
function displayDutyDetailsModal(duty) {
    currentDutyId = duty.id;

    let personnelTable = '';
    if (duty.personnel.length > 0) {
        personnelTable = `
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الرتبة</th>
                        <th>المنصب</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${duty.personnel.map(person => `
                        <tr>
                            <td>${person.name}</td>
                            <td>${person.rank}</td>
                            <td>${person.position || '-'}</td>
                            <td>${person.status}</td>
                            <td>${person.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } else {
        personnelTable = '<p class="text-muted">لا توجد أفراد مسجلين في هذا الواجب</p>';
    }

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <strong>التاريخ:</strong> ${duty.duty_date}
            </div>
            <div class="col-md-6">
                <strong>الوقت:</strong> ${duty.duty_time}
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-12">
                <strong>الموقع:</strong> ${duty.location.name} (${duty.location.serial_number})
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <strong>الأفراد المكلفين:</strong>
                ${personnelTable}
            </div>
        </div>
        ${duty.notes ? `
        <div class="row mt-3">
            <div class="col-md-12">
                <strong>ملاحظات عامة:</strong>
                <p class="border p-2 bg-light">${duty.notes}</p>
            </div>
        </div>
        ` : ''}
        <div class="row mt-2">
            <div class="col-md-6">
                <small class="text-muted">تاريخ الإنشاء: ${duty.created_at}</small>
            </div>
            <div class="col-md-6">
                <small class="text-muted">آخر تحديث: ${duty.updated_at}</small>
            </div>
        </div>
    `;

    document.getElementById('dutyDetailsBody').innerHTML = detailsHtml;

    const modal = new bootstrap.Modal(document.getElementById('dutyDetailsModal'));
    modal.show();
}

// تأكيد حذف الواجب
function confirmDeleteDuty(dutyId) {
    if (confirm('هل أنت متأكد من حذف هذا الواجب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        currentDutyId = dutyId;
        deleteDuty();
    }
}

// حذف الواجب
function deleteDuty() {
    if (!currentDutyId) return;

    fetch(`/duties/api/delete-duty/${currentDutyId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف الواجب بنجاح', 'success');

            // إغلاق Modal إذا كان مفتوحاً
            const modal = bootstrap.Modal.getInstance(document.getElementById('dutyDetailsModal'));
            if (modal) modal.hide();

            // إعادة تحميل السجل
            loadDutiesHistory();

            currentDutyId = null;
        } else {
            showAlert('خطأ في حذف الواجب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حذف الواجب:', error);
        showAlert('خطأ في حذف الواجب', 'error');
    });
}

// حفظ كقالب
function saveAsTemplate() {
    const locationId = document.getElementById('locationSelect').value;

    if (!locationId) {
        showAlert('يرجى اختيار الموقع أولاً', 'error');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('saveTemplateModal'));
    modal.show();
}

// تأكيد حفظ القالب
function confirmSaveTemplate() {
    const templateName = document.getElementById('templateName').value.trim();
    const locationId = document.getElementById('locationSelect').value;

    if (!templateName) {
        showAlert('يرجى إدخال اسم القالب', 'error');
        return;
    }

    // جمع بيانات الجدول
    const rows = document.querySelectorAll('#dutyTableBody tr');
    const templateData = {
        rows: [],
        notes: document.getElementById('generalNotes').value
    };

    rows.forEach((row, index) => {
        const personnelSelect = row.querySelector('.personnel-select');
        const positionInput = row.querySelector('.position-input');
        const statusSelect = row.querySelector('.status-select');
        const notesInput = row.querySelector('.notes-input');

        templateData.rows.push({
            personnel_id: personnelSelect.value,
            position: positionInput.value,
            status: statusSelect.value,
            notes: notesInput.value
        });
    });

    // إرسال القالب
    fetch('/duties/api/save-template', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: templateName,
            location_id: parseInt(locationId),
            template_data: templateData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حفظ القالب بنجاح', 'success');

            // إغلاق Modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('saveTemplateModal'));
            modal.hide();

            // مسح اسم القالب
            document.getElementById('templateName').value = '';

            // إعادة تحميل القوالب
            loadTemplates(locationId);
        } else {
            showAlert('خطأ في حفظ القالب: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ القالب:', error);
        showAlert('خطأ في حفظ القالب', 'error');
    });
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer');

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    alertContainer.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}
